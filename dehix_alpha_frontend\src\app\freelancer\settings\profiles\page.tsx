'use client';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/lib/store';
import SidebarMenu from '@/components/menu/sidebarMenu';
import {
  menuItemsBottom,
  menuItemsTop,
} from '@/config/menuItems/freelancer/settingsMenuItems';
import Header from '@/components/header/header';
import { axiosInstance } from '@/lib/axiosinstance';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Plus, X, Edit } from 'lucide-react';
import { FreelancerProfile } from '@/types/freelancer';
import AddEditProfileDialog from '@/components/dialogs/addEditProfileDialog';
export default function ProfilesPage() {
  const user = useSelector((state: RootState) => state.user);
  const [profiles, setProfiles] = useState<FreelancerProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newProfileName, setNewProfileName] = useState('');
  const [newProfileDescription, setNewProfileDescription] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState<string | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProfile, setEditingProfile] =
    useState<FreelancerProfile | null>(null);

  useEffect(() => {
    fetchProfiles();
  }, [user.uid]);

  const fetchProfiles = async () => {
    if (!user.uid) return;

    setIsLoading(true);
    try {
      const response = await axiosInstance.get(`/freelancer/profiles`);
      const profilesData = response.data.data || [];
      setProfiles(profilesData);

      // Set the first profile as active tab, or empty string if no profiles
      if (profilesData.length > 0 && !activeTab && profilesData[0]._id) {
        setActiveTab(profilesData[0]._id);
      }
    } catch (error) {
      console.error('Error fetching profiles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load profiles',
        variant: 'destructive',
      });
      setProfiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProfile = async () => {
    if (!newProfileName.trim()) {
      toast({
        title: 'Error',
        description: 'Profile name is required',
        variant: 'destructive',
      });
      return;
    }

    const description =
      newProfileDescription.trim() ||
      `Professional profile for ${newProfileName.trim()}. This profile showcases my skills and experience in this domain.`;

    if (description.length < 10) {
      toast({
        title: 'Error',
        description: 'Description must be at least 10 characters long',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await axiosInstance.post(`/freelancer/profile`, {
        profileName: newProfileName.trim(),
        description: description,
        skills: [],
        domains: [],
        projects: [],
        experiences: [],
        education: [],
        portfolioLinks: [],
      });

      const newProfile = response.data.data;
      setProfiles([...profiles, newProfile]);
      if (newProfile._id) {
        setActiveTab(newProfile._id);
      }
      setNewProfileName('');
      setNewProfileDescription('');
      setIsCreateDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Profile created successfully',
      });
    } catch (error) {
      console.error('Error creating profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to create profile',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteProfile = (profileId: string) => {
    setProfileToDelete(profileId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteProfile = async () => {
    if (!profileToDelete) return;

    try {
      await axiosInstance.delete(`/freelancer/profile/${profileToDelete}`);

      // If deleting the active tab, switch to another tab
      if (activeTab === profileToDelete) {
        const remainingProfiles = profiles.filter(
          (p) => p._id !== profileToDelete,
        );
        setActiveTab(
          remainingProfiles.length > 0 && remainingProfiles[0]._id
            ? remainingProfiles[0]._id
            : '',
        );
      }

      toast({
        title: 'Profile Deleted',
        description: 'Profile has been successfully deleted.',
      });
      fetchProfiles();
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete profile',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setProfileToDelete(null);
    }
  };

  const handleEditProfile = (profile: FreelancerProfile) => {
    setEditingProfile(profile);
    setIsEditDialogOpen(true);
  };

  const handleProfileSaved = () => {
    fetchProfiles();
    setIsEditDialogOpen(false);
    setEditingProfile(null);
  };

  return (
    <div className="flex min-h-screen w-full flex-col bg-muted/40">
      <SidebarMenu
        menuItemsTop={menuItemsTop}
        menuItemsBottom={menuItemsBottom}
        active="Profiles"
        isKycCheck={true}
      />
      <div className="flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8">
        <Header
          menuItemsTop={menuItemsTop}
          menuItemsBottom={menuItemsBottom}
          activeMenu="Profiles"
          breadcrumbItems={[
            { label: 'Freelancer', link: '/dashboard/freelancer' },
            { label: 'Settings', link: '#' },
            { label: 'Profiles', link: '#' },
          ]}
        />
        <main className="grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold">Professional Profiles</h1>
                <p className="text-muted-foreground">
                  Create and manage multiple professional profiles to showcase
                  different aspects of your expertise.
                </p>
              </div>
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Profile
              </Button>
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <p>Loading profiles...</p>
              </div>
            ) : profiles.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-semibold mb-2">
                  No profiles added
                </h3>
                <p className="text-muted-foreground mb-4">
                  Create your first professional profile to get started.
                </p>
              </div>
            ) : (
              <div className="w-full">
                {/* Custom Tab Navigation */}
                <div className="flex border-b border-gray-200 dark:border-gray-700">
                  {profiles
                    .filter((profile) => profile._id)
                    .map((profile, index) => (
                      <button
                        key={profile._id}
                        onClick={() => setActiveTab(profile._id!)}
                        className={`relative px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                          activeTab === profile._id
                            ? 'text-white bg-gray-800 dark:bg-gray-700'
                            : 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200'
                        } ${index === 0 ? 'rounded-tl-lg' : ''} ${
                          index === profiles.filter((p) => p._id).length - 1
                            ? 'rounded-tr-lg'
                            : ''
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <span>{profile.profileName}</span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteProfile(profile._id!);
                            }}
                            className="ml-1 text-red-500 hover:text-red-700 opacity-70 hover:opacity-100"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      </button>
                    ))}
                </div>

                {/* Tab Content */}
                {profiles
                  .filter((profile) => profile._id)
                  .map((profile) => (
                    <div
                      key={profile._id}
                      className={`${
                        activeTab === profile._id ? 'block' : 'hidden'
                      }`}
                    >
                      <div className="bg-white dark:bg-gray-900 border border-t-0 border-gray-200 dark:border-gray-700 rounded-b-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {profile.profileName}
                          </h2>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditProfile(profile)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Profile
                          </Button>
                        </div>

                        {/* Profile Content */}
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                              Description
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">
                              {profile.description || 'No description provided'}
                            </p>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                              Skills
                            </h3>
                            <div className="flex flex-wrap gap-2">
                              {profile.skills && profile.skills.length > 0 ? (
                                profile.skills.map(
                                  (skill: any, index: number) => (
                                    <span
                                      key={index}
                                      className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                                    >
                                      {typeof skill === 'string'
                                        ? skill
                                        : skill.label || skill.name}
                                    </span>
                                  ),
                                )
                              ) : (
                                <p className="text-gray-500 dark:text-gray-400 italic">
                                  No skills added
                                </p>
                              )}
                            </div>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                              Domains
                            </h3>
                            <div className="flex flex-wrap gap-2">
                              {profile.domains && profile.domains.length > 0 ? (
                                profile.domains.map(
                                  (domain: any, index: number) => (
                                    <span
                                      key={index}
                                      className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm"
                                    >
                                      {typeof domain === 'string'
                                        ? domain
                                        : domain.label || domain.name}
                                    </span>
                                  ),
                                )
                              ) : (
                                <p className="text-gray-500 dark:text-gray-400 italic">
                                  No domains added
                                </p>
                              )}
                            </div>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                              Projects
                            </h3>
                            {profile.projects && profile.projects.length > 0 ? (
                              <div className="space-y-3">
                                {profile.projects.map(
                                  (project: any, index: number) => (
                                    <div
                                      key={index}
                                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                                    >
                                      <h4 className="font-medium text-gray-900 dark:text-white">
                                        {project.projectName || project.name}
                                      </h4>
                                      {project.description && (
                                        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                                          {project.description}
                                        </p>
                                      )}
                                    </div>
                                  ),
                                )}
                              </div>
                            ) : (
                              <p className="text-gray-500 dark:text-gray-400 italic">
                                No projects added
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Create Profile Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Profile</DialogTitle>
            <DialogDescription>
              Enter a name and description for your new professional profile.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Profile Name</label>
              <Input
                placeholder="e.g., Frontend Developer, Backend Engineer"
                value={newProfileName}
                onChange={(e) => setNewProfileName(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <label className="text-sm font-medium">
                Description (optional)
              </label>
              <Textarea
                placeholder="Describe your expertise and experience in this area... (minimum 10 characters if provided)"
                value={newProfileDescription}
                onChange={(e) => setNewProfileDescription(e.target.value)}
                className="mt-1"
                rows={3}
              />
              <p className="text-xs text-muted-foreground mt-1">
                If left empty, a default description will be generated.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateDialogOpen(false);
                setNewProfileName('');
                setNewProfileDescription('');
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateProfile}>Create Profile</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Profile Dialog */}
      <AddEditProfileDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        profile={editingProfile}
        onProfileSaved={handleProfileSaved}
        freelancerId={user.uid}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Profile</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this profile? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteProfile}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
