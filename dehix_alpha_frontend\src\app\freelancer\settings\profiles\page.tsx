'use client';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/lib/store';
import SidebarMenu from '@/components/menu/sidebarMenu';
import {
  menuItemsBottom,
  menuItemsTop,
} from '@/config/menuItems/freelancer/settingsMenuItems';
import Header from '@/components/header/header';
import { axiosInstance } from '@/lib/axiosinstance';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Plus, X, Edit } from 'lucide-react';
import { FreelancerProfile } from '@/types/freelancer';
import AddEditProfileDialog from '@/components/dialogs/addEditProfileDialog';
export default function ProfilesPage() {
  const user = useSelector((state: RootState) => state.user);
  const [profiles, setProfiles] = useState<FreelancerProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newProfileName, setNewProfileName] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState<string | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProfile, setEditingProfile] =
    useState<FreelancerProfile | null>(null);

  useEffect(() => {
    fetchProfiles();
  }, [user.uid]);

  const fetchProfiles = async () => {
    if (!user.uid) return;

    setIsLoading(true);
    try {
      const response = await axiosInstance.get(`/freelancer/profiles`);
      const profilesData = response.data.data || [];
      setProfiles(profilesData);

      // Set the first profile as active tab, or empty string if no profiles
      if (profilesData.length > 0 && !activeTab && profilesData[0]._id) {
        setActiveTab(profilesData[0]._id);
      }
    } catch (error) {
      console.error('Error fetching profiles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load profiles',
        variant: 'destructive',
      });
      setProfiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProfile = async () => {
    if (!newProfileName.trim()) {
      toast({
        title: 'Error',
        description: 'Profile name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await axiosInstance.post(`/freelancer/profile`, {
        profileName: newProfileName.trim(),
        description: '',
        skills: [],
        domains: [],
        projects: [],
        experiences: [],
        education: [],
        portfolioLinks: [],
      });

      const newProfile = response.data.data;
      setProfiles([...profiles, newProfile]);
      if (newProfile._id) {
        setActiveTab(newProfile._id);
      }
      setNewProfileName('');
      setIsCreateDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Profile created successfully',
      });
    } catch (error) {
      console.error('Error creating profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to create profile',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteProfile = (profileId: string) => {
    setProfileToDelete(profileId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteProfile = async () => {
    if (!profileToDelete) return;

    try {
      await axiosInstance.delete(`/freelancer/profile/${profileToDelete}`);

      // If deleting the active tab, switch to another tab
      if (activeTab === profileToDelete) {
        const remainingProfiles = profiles.filter(
          (p) => p._id !== profileToDelete,
        );
        setActiveTab(
          remainingProfiles.length > 0 && remainingProfiles[0]._id
            ? remainingProfiles[0]._id
            : '',
        );
      }

      toast({
        title: 'Profile Deleted',
        description: 'Profile has been successfully deleted.',
      });
      fetchProfiles();
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete profile',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setProfileToDelete(null);
    }
  };

  const handleEditProfile = (profile: FreelancerProfile) => {
    setEditingProfile(profile);
    setIsEditDialogOpen(true);
  };

  const handleProfileSaved = () => {
    fetchProfiles();
    setIsEditDialogOpen(false);
    setEditingProfile(null);
  };

  const handleToggleStatus = async (profileId: string, isActive: boolean) => {
    try {
      await axiosInstance.patch(
        `/freelancer/profile/${profileId}/toggle-status`,
        {
          isActive,
        },
      );
      toast({
        title: isActive ? 'Profile Activated' : 'Profile Deactivated',
        description: `Profile has been ${isActive ? 'activated' : 'deactivated'}.`,
      });
      fetchProfiles();
    } catch (error) {
      console.error('Error updating profile status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update profile status',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="flex min-h-screen w-full flex-col bg-muted/40">
      <SidebarMenu
        menuItemsTop={menuItemsTop}
        menuItemsBottom={menuItemsBottom}
        active="Profiles"
        isKycCheck={true}
      />
      <div className="flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8">
        <Header
          menuItemsTop={menuItemsTop}
          menuItemsBottom={menuItemsBottom}
          activeMenu="Profiles"
          breadcrumbItems={[
            { label: 'Freelancer', link: '/dashboard/freelancer' },
            { label: 'Settings', link: '#' },
            { label: 'Profiles', link: '#' },
          ]}
        />
        <main className="grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold">Professional Profiles</h1>
                <p className="text-muted-foreground">
                  Create and manage multiple professional profiles to showcase
                  different aspects of your expertise.
                </p>
              </div>
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Profile
              </Button>
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <p>Loading profiles...</p>
              </div>
            ) : profiles.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-semibold mb-2">
                  No profiles added
                </h3>
                <p className="text-muted-foreground mb-4">
                  Create your first professional profile to get started.
                </p>
              </div>
            ) : (
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-auto">
                  {profiles
                    .filter((profile) => profile._id)
                    .map((profile) => (
                      <TabsTrigger
                        key={profile._id}
                        value={profile._id!}
                        className="flex items-center gap-2"
                      >
                        {profile.profileName}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteProfile(profile._id!);
                          }}
                          className="ml-1 text-red-500 hover:text-red-700"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </TabsTrigger>
                    ))}
                </TabsList>

                {profiles
                  .filter((profile) => profile._id)
                  .map((profile) => (
                    <TabsContent
                      key={profile._id}
                      value={profile._id!}
                      className="mt-6"
                    >
                      <div className="border rounded-lg p-6">
                        <div className="flex justify-between items-center mb-4">
                          <h2 className="text-xl font-semibold">
                            {profile.profileName}
                          </h2>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditProfile(profile)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Profile
                          </Button>
                        </div>
                        {/* Profile form content will go here */}
                        <p className="text-muted-foreground">
                          Profile form content coming soon...
                        </p>
                      </div>
                    </TabsContent>
                  ))}
              </Tabs>
            )}
          </div>
        </main>
      </div>

      {/* Create Profile Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Profile</DialogTitle>
            <DialogDescription>
              Enter a name for your new professional profile.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              placeholder="e.g., Frontend Developer, Backend Engineer"
              value={newProfileName}
              onChange={(e) => setNewProfileName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCreateProfile();
                }
              }}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateDialogOpen(false);
                setNewProfileName('');
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateProfile}>Create Profile</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Profile</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this profile? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteProfile}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
