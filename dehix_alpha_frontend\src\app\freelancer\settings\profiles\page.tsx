'use client';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/lib/store';
import SidebarMenu from '@/components/menu/sidebarMenu';
import {
  menuItemsBottom,
  menuItemsTop,
} from '@/config/menuItems/freelancer/settingsMenuItems';
import Header from '@/components/header/header';
import { axiosInstance } from '@/lib/axiosinstance';
import { toast } from '@/components/ui/use-toast';
import FreelancerProfileCard from '@/components/cards/freelancerProfileCard';
import AddProfileCard from '@/components/cards/addProfileCard';
import AddEditProfileDialog from '@/components/dialogs/addEditProfileDialog';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FreelancerProfile } from '@/types/freelancer';

export default function ProfilesPage() {
  const user = useSelector((state: RootState) => state.user);
  const [profiles, setProfiles] = useState<FreelancerProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProfile, setEditingProfile] =
    useState<FreelancerProfile | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState<string | null>(null);

  useEffect(() => {
    fetchProfiles();
  }, [user.uid]);

  const fetchProfiles = async () => {
    if (!user.uid) return;

    setIsLoading(true);
    try {
      const response = await axiosInstance.get(`/freelancer/profiles`);
      setProfiles(response.data.data || []);
    } catch (error) {
      console.error('Error fetching profiles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load profiles',
        variant: 'destructive',
      });
      setProfiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddProfile = () => {
    setEditingProfile(null);
    setIsDialogOpen(true);
  };

  const handleEditProfile = (profile: FreelancerProfile) => {
    setEditingProfile(profile);
    setIsDialogOpen(true);
  };

  const handleDeleteProfile = (profileId: string) => {
    setProfileToDelete(profileId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteProfile = async () => {
    if (!profileToDelete) return;

    try {
      await axiosInstance.delete(`/freelancer/profile/${profileToDelete}`);
      toast({
        title: 'Profile Deleted',
        description: 'Profile has been successfully deleted.',
      });
      fetchProfiles();
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete profile',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setProfileToDelete(null);
    }
  };

  const handleViewProfile = (profile: FreelancerProfile) => {
    // TODO: Implement profile view modal or navigate to profile view page
    console.log('View profile:', profile);
  };

  const handleToggleStatus = async (profileId: string, isActive: boolean) => {
    try {
      await axiosInstance.patch(
        `/freelancer/profile/${profileId}/toggle-status`,
        {
          isActive,
        },
      );
      toast({
        title: isActive ? 'Profile Activated' : 'Profile Deactivated',
        description: `Profile has been ${isActive ? 'activated' : 'deactivated'}.`,
      });
      fetchProfiles();
    } catch (error) {
      console.error('Error updating profile status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update profile status',
        variant: 'destructive',
      });
    }
  };

  const handleProfileSaved = () => {
    fetchProfiles();
  };

  return (
    <div className="flex min-h-screen w-full flex-col bg-muted/40">
      <SidebarMenu
        menuItemsTop={menuItemsTop}
        menuItemsBottom={menuItemsBottom}
        active="Profiles"
        isKycCheck={true}
      />
      <div className="flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8">
        <Header
          menuItemsTop={menuItemsTop}
          menuItemsBottom={menuItemsBottom}
          activeMenu="Profiles"
          breadcrumbItems={[
            { label: 'Freelancer', link: '/dashboard/freelancer' },
            { label: 'Settings', link: '#' },
            { label: 'Profiles', link: '#' },
          ]}
        />
        <main className="grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8">
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl font-bold">Professional Profiles</h1>
              <p className="text-muted-foreground">
                Create and manage multiple professional profiles to showcase
                different aspects of your expertise.
              </p>
            </div>

            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <Skeleton key={index} className="h-[400px] w-full" />
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <AddProfileCard onClick={handleAddProfile} />
                {profiles.map((profile) => (
                  <FreelancerProfileCard
                    key={profile._id}
                    profile={profile}
                    onEdit={handleEditProfile}
                    onDelete={handleDeleteProfile}
                    onView={handleViewProfile}
                    onToggleStatus={handleToggleStatus}
                  />
                ))}
              </div>
            )}

            {!isLoading && profiles.length === 0 && (
              <div className="text-center py-12">
                <h3 className="text-lg font-semibold mb-2">No profiles yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first professional profile to get started.
                </p>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Add/Edit Profile Dialog */}
      <AddEditProfileDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        profile={editingProfile}
        onProfileSaved={handleProfileSaved}
        freelancerId={user.uid}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Profile</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this profile? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteProfile}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
