/**
 * File: get.ts
 * Author: Akhil
 * Date: 22-05-2024
 * Description: Schema for API to get all reports
 */

import { FastifySchema } from "fastify";
import { commonErrorResponses } from "../commonErrorCodes";

export const getReportsSchema: FastifySchema = {
  description: "API to get all reports",
  tags: ["Reports"],
  querystring: {
    type: "object",
    properties: {
      "filter[report_type]": { type: "string" },
      "filter[status]": { type: "string" },
      "filter[search][value]": { type: "string" },
      "filter[search][columns]": { type: "string" },
      page: { type: "string" },
      limit: { type: "string" },
    },
  },
  response: {
    200: {
      description: "Success",
      type: "object",
      properties: {
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              _id: { type: "string" },
              report_type: { type: "string" },
              description: { type: "string" },
              subject:{type: "string"},
              reportedBy: { type: "string" },
              reportedById: { type: "string" },
              status: { type: "string" },
              createdAt: {
                type: "string",
                format: "date-time",
              },
              updatedAt: {
                type: "string",
                format: "date-time",
              },
            },
            required: [],
          },
        },
      },
    },
    ...commonErrorResponses,
  },
};
