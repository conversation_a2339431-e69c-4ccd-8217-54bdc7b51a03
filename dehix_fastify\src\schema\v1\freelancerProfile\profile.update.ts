import { FastifySchema } from "fastify";
import { commonErrorResponses } from "../commonErrorCodes";

export const updateFreelancerProfileSchema: FastifySchema = {
  description: "API to update a freelancer profile",
  tags: ["Freelancer Profile"],
  params: {
    type: "object",
    required: ["profile_id"],
    properties: {
      profile_id: {
        type: "string",
        description: "Profile ID",
      },
    },
    additionalProperties: false,
  },
  body: {
    type: "object",
    properties: {
      profileName: {
        type: "string",
        minLength: 1,
        maxLength: 100,
        description: "Name of the profile",
      },
      description: {
        type: "string",
        minLength: 10,
        maxLength: 500,
        description: "Detailed description of the profile",
      },
      skills: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of skill IDs",
      },
      domains: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of domain IDs",
      },
      projects: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of project IDs",
      },
      experiences: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of experience IDs",
      },
      education: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of education IDs",
      },
      portfolioLinks: {
        type: "array",
        items: {
          type: "string",
          format: "uri",
        },
        description: "Array of portfolio links",
      },
      githubLink: {
        type: "string",
        format: "uri",
        description: "GitHub profile URL",
      },
      linkedinLink: {
        type: "string",
        format: "uri",
        description: "LinkedIn profile URL",
      },
      personalWebsite: {
        type: "string",
        format: "uri",
        description: "Personal website URL",
      },
      hourlyRate: {
        type: "number",
        minimum: 0,
        description: "Preferred hourly rate in USD",
      },
      availability: {
        type: "string",
        enum: ["FULL_TIME", "PART_TIME", "CONTRACT", "FREELANCE"],
        description: "Availability type",
      },
      isActive: {
        type: "boolean",
        description: "Whether the profile is active",
      },
    },
    additionalProperties: false,
  },
  response: {
    200: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            _id: { type: "string" },
            freelancerId: { type: "string" },
            profileName: { type: "string" },
            description: { type: "string" },
            skills: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
            domains: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
            projects: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  projectName: { type: "string" },
                  description: { type: "string" },
                  role: { type: "string" },
                  start: { type: "string" },
                  end: { type: "string" },
                  techUsed: {
                    type: "array",
                    items: { type: "string" },
                  },
                  githubLink: { type: "string" },
                  projectType: { type: "string" },
                  verified: { type: "boolean" },
                },
              },
            },
            portfolioLinks: {
              type: "array",
              items: { type: "string" },
            },
            githubLink: { type: "string" },
            linkedinLink: { type: "string" },
            personalWebsite: { type: "string" },
            hourlyRate: { type: "number" },
            availability: { type: "string" },
            isActive: { type: "boolean" },
            createdAt: { type: "string" },
            updatedAt: { type: "string" },
          },
        },
      },
    },
    ...commonErrorResponses,
  },
};
