import { FastifySchema } from "fastify";
import { commonErrorResponses } from "../commonErrorCodes";

export const getFreelancerProfilesSchema: FastifySchema = {
  description: "API to get all profiles for a freelancer",
  tags: ["Freelancer Profile"],
  response: {
    200: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "array",
          items: {
            type: "object",
            properties: {
              _id: { type: "string" },
              freelancerId: { type: "string" },
              profileName: { type: "string" },
              description: { type: "string" },
              skills: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    _id: { type: "string" },
                    name: { type: "string" },
                  },
                },
              },
              domains: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    _id: { type: "string" },
                    name: { type: "string" },
                  },
                },
              },
              projects: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    _id: { type: "string" },
                    projectName: { type: "string" },
                    description: { type: "string" },
                    role: { type: "string" },
                    start: { type: "string" },
                    end: { type: "string" },
                    techUsed: {
                      type: "array",
                      items: { type: "string" },
                    },
                    githubLink: { type: "string" },
                    projectType: { type: "string" },
                    verified: { type: "boolean" },
                  },
                },
              },
              portfolioLinks: {
                type: "array",
                items: { type: "string" },
              },
              githubLink: { type: "string" },
              linkedinLink: { type: "string" },
              personalWebsite: { type: "string" },
              hourlyRate: { type: "number" },
              availability: { type: "string" },
              isActive: { type: "boolean" },
              createdAt: { type: "string" },
              updatedAt: { type: "string" },
            },
          },
        },
      },
    },
    ...commonErrorResponses,
  },
};

export const getFreelancerProfileByIdSchema: FastifySchema = {
  description: "API to get a specific freelancer profile by ID",
  tags: ["Freelancer Profile"],
  params: {
    type: "object",
    required: ["profile_id"],
    properties: {
      profile_id: {
        type: "string",
        description: "Profile ID",
      },
    },
    additionalProperties: false,
  },
  response: {
    200: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            _id: { type: "string" },
            freelancerId: { type: "string" },
            profileName: { type: "string" },
            description: { type: "string" },
            skills: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
            domains: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  name: { type: "string" },
                },
              },
            },
            projects: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  _id: { type: "string" },
                  projectName: { type: "string" },
                  description: { type: "string" },
                  role: { type: "string" },
                  start: { type: "string" },
                  end: { type: "string" },
                  techUsed: {
                    type: "array",
                    items: { type: "string" },
                  },
                  githubLink: { type: "string" },
                  projectType: { type: "string" },
                  verified: { type: "boolean" },
                },
              },
            },
            portfolioLinks: {
              type: "array",
              items: { type: "string" },
            },
            githubLink: { type: "string" },
            linkedinLink: { type: "string" },
            personalWebsite: { type: "string" },
            hourlyRate: { type: "number" },
            availability: { type: "string" },
            isActive: { type: "boolean" },
            createdAt: { type: "string" },
            updatedAt: { type: "string" },
          },
        },
      },
    },
    ...commonErrorResponses,
  },
};
