{"name": "dehix", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx", "lint-fix": "eslint . --ext .ts,.tsx --fix"}, "dependencies": {"@emoji-mart/react": "^1.1.1", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.8.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.2.6", "@tanstack/react-table": "^8.20.6", "@types/js-cookie": "^3.0.6", "@types/react-select": "^5.0.0", "axios": "^1.7.2", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "compromise": "^14.14.4", "cookie": "^0.6.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "firebase": "^10.11.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "lucide-react": "^0.367.0", "natural": "^8.0.1", "next": "^14.1.4", "next-themes": "^0.3.0", "react": "^18.3.1", "react-autosuggest": "^10.1.0", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-datepicker": "^7.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-markdown": "^9.0.3", "react-redux": "^9.1.2", "react-select": "^5.8.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@next/eslint-plugin-next": "^14.2.4", "@types/node": "^20", "@types/react": "^18", "@types/react-autosuggest": "^10.1.11", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "^10.0.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.1.6", "postcss": "^8", "prettier": "^3.3.2", "tailwindcss": "^3.3.0", "typescript": "5.5.x"}}