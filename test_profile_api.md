# Freelancer Profile API Testing Guide

This document provides instructions for testing the newly implemented freelancer profile management system.

## Backend API Endpoints

### 1. Create Profile
**POST** `/freelancer/profile`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Body:**
```json
{
  "profileName": "Frontend Developer",
  "description": "Experienced React and TypeScript developer with 5+ years of experience building modern web applications.",
  "skills": ["skill_id_1", "skill_id_2"],
  "domains": ["domain_id_1"],
  "projects": ["project_id_1", "project_id_2"],
  "experiences": ["experience_id_1"],
  "education": ["education_id_1"],
  "portfolioLinks": ["https://myportfolio.com", "https://github.com/myprojects"],
  "githubLink": "https://github.com/username",
  "linkedinLink": "https://linkedin.com/in/username",
  "personalWebsite": "https://mywebsite.com",
  "hourlyRate": 75,
  "availability": "FREELANCE"
}
```

### 2. Get All Profiles
**GET** `/freelancer/profiles`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

### 3. Get Profile by ID
**GET** `/freelancer/profile/:profile_id`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

### 4. Update Profile
**PUT** `/freelancer/profile/:profile_id`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Body:**
```json
{
  "profileName": "Senior Frontend Developer",
  "description": "Updated description with more experience...",
  "hourlyRate": 85,
  "isActive": true
}
```

### 5. Delete Profile
**DELETE** `/freelancer/profile/:profile_id`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

### 6. Toggle Profile Status
**PATCH** `/freelancer/profile/:profile_id`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Body:**
```json
{
  "isActive": false
}
```

## Frontend Testing

### 1. Navigate to Profiles Page
1. Login as a freelancer
2. Go to Settings → Profiles
3. Verify the page loads with the "Add Profile" card

### 2. Create a New Profile
1. Click on the "Add Profile" card
2. Fill in the profile form:
   - Profile Name: "Frontend Developer"
   - Description: "Experienced developer..."
   - Select skills, domains, projects, etc.
   - Add portfolio links
   - Set hourly rate and availability
3. Click "Create Profile"
4. Verify the profile appears in the list

### 3. Edit Profile
1. Click the edit icon on a profile card
2. Modify some fields
3. Click "Update Profile"
4. Verify changes are reflected

### 4. Toggle Profile Status
1. Click "Deactivate" on an active profile
2. Verify the status changes and button text updates
3. Click "Activate" to reactivate

### 5. Delete Profile
1. Click the delete icon on a profile card
2. Confirm deletion in the dialog
3. Verify the profile is removed from the list

## Expected Behaviors

### Success Cases
- Profile creation with valid data succeeds
- Profile list displays all user's profiles
- Profile editing updates the data correctly
- Profile deletion removes the profile
- Status toggling works as expected

### Error Cases
- Duplicate profile names are rejected
- Invalid URLs are rejected
- Unauthorized access to other users' profiles is blocked
- Required fields validation works
- Character limits are enforced

## Database Verification

Check the MongoDB collection `freelancerprofiles` to verify:
1. Profiles are created with correct data
2. Updates modify the existing documents
3. Deletions remove the documents
4. Indexes are working properly

## Performance Considerations

- Profile listing should be fast even with many profiles
- Populated fields (skills, domains, projects) should load efficiently
- Search and filtering should be responsive

## Security Verification

- Ensure users can only access their own profiles
- Verify JWT authentication is required for all endpoints
- Check that profile ownership is validated on all operations
