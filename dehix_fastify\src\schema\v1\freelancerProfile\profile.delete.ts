import { FastifySchema } from "fastify";
import { commonErrorResponses } from "../commonErrorCodes";

export const deleteFreelancerProfileSchema: FastifySchema = {
  description: "API to delete a freelancer profile",
  tags: ["Freelancer Profile"],
  params: {
    type: "object",
    required: ["profile_id"],
    properties: {
      profile_id: {
        type: "string",
        description: "Profile ID to delete",
      },
    },
    additionalProperties: false,
  },
  response: {
    200: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },
    ...commonErrorResponses,
  },
};
