"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfile, setEditingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            setProfiles(response.data.data || []);\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddProfile = ()=>{\n        setEditingProfile(null);\n        setIsDialogOpen(true);\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsDialogOpen(true);\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleViewProfile = (profile)=>{\n        // TODO: Implement profile view modal or navigate to profile view page\n        console.log(\"View profile:\", profile);\n    };\n    const handleToggleStatus = async (profileId, isActive)=>{\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.patch(\"/freelancer/profile/\".concat(profileId, \"/toggle-status\"), {\n                isActive\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: isActive ? \"Profile Activated\" : \"Profile Deactivated\",\n                description: \"Profile has been \".concat(isActive ? \"activated\" : \"deactivated\", \".\")\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error updating profile status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Professional Profiles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        ...Array(3)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                            className: \"h-[400px] w-full\"\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddProfileCard, {\n                                            onClick: handleAddProfile\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FreelancerProfileCard, {\n                                                profile: profile,\n                                                onEdit: handleEditProfile,\n                                                onDelete: handleDeleteProfile,\n                                                onView: handleViewProfile,\n                                                onToggleStatus: handleToggleStatus\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                !isLoading && profiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles yet\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddEditProfileDialog, {\n                open: isDialogOpen,\n                onOpenChange: setIsDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"CkLx0GFLTcqAKXa30ooXVDm4ThI=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_9__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});