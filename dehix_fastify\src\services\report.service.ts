import { ReportModel } from '../models/report.entity';
import { CreateReportBody } from '../types/v1/report/createReport';
import { Service, Inject } from "fastify-decorators";
import { NotFoundError } from "../common/errors";
import { ERROR_CODES, RESPONSE_MESSAGE } from "../common/constants";
import { ReportDAO } from '../dao/report.dao';
@Service()
export class ReportService {
  @Inject(ReportDAO)
  private ReportDAO!: ReportDAO;

  async createReport(data: CreateReportBody) {
    try {
      return await ReportModel.create(data);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllReports(
      filters: Record<string, string[]>,
      page: string,
      limit: string,
    ) {  
      const skills: any = await this.ReportDAO.getReports(filters, page, limit);
  
      if (!skills) {
        throw new NotFoundError(
          RESPONSE_MESSAGE.NOT_FOUND("Skills"),
          ERROR_CODES.FREELANCER_NOT_FOUND,
        );
      }
  
      return skills;
    }
}
