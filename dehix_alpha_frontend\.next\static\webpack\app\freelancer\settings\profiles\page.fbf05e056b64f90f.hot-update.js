"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dialogs/addEditProfileDialog */ \"(app-pages-browser)/./src/components/dialogs/addEditProfileDialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_16__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProfileName, setNewProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newProfileDescription, setNewProfileDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfile, setEditingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingProfileData, setEditingProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [skillsOptions, setSkillsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [domainsOptions, setDomainsOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n        fetchSkillsAndDomains();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            const profilesData = response.data.data || [];\n            setProfiles(profilesData);\n            // Set the first profile as active tab, or empty string if no profiles\n            if (profilesData.length > 0 && !activeTab && profilesData[0]._id) {\n                setActiveTab(profilesData[0]._id);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchSkillsAndDomains = async ()=>{\n        try {\n            // Fetch skills\n            const skillsResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/skills\");\n            const skillsData = skillsResponse.data.data || [];\n            setSkillsOptions(Array.isArray(skillsData) ? skillsData : Object.values(skillsData));\n            // Fetch domains\n            const domainsResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/domains\");\n            const domainsData = domainsResponse.data.data || [];\n            setDomainsOptions(Array.isArray(domainsData) ? domainsData : Object.values(domainsData));\n        } catch (error) {\n            console.error(\"Error fetching skills and domains:\", error);\n        }\n    };\n    const handleCreateProfile = async ()=>{\n        if (!newProfileName.trim()) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const description = newProfileDescription.trim() || \"Professional profile for \".concat(newProfileName.trim(), \". This profile showcases my skills and experience in this domain.\");\n        if (description.length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.post(\"/freelancer/profile\", {\n                profileName: newProfileName.trim(),\n                description: description,\n                skills: [],\n                domains: [],\n                projects: [],\n                experiences: [],\n                education: [],\n                portfolioLinks: []\n            });\n            const newProfile = response.data.data;\n            setProfiles([\n                ...profiles,\n                newProfile\n            ]);\n            if (newProfile._id) {\n                setActiveTab(newProfile._id);\n            }\n            setNewProfileName(\"\");\n            setNewProfileDescription(\"\");\n            setIsCreateDialogOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Success\",\n                description: \"Profile created successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error creating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to create profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            // If deleting the active tab, switch to another tab\n            if (activeTab === profileToDelete) {\n                const remainingProfiles = profiles.filter((p)=>p._id !== profileToDelete);\n                setActiveTab(remainingProfiles.length > 0 && remainingProfiles[0]._id ? remainingProfiles[0]._id : \"\");\n            }\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsEditDialogOpen(true);\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n        setIsEditDialogOpen(false);\n        setEditingProfile(null);\n    };\n    const handleUpdateProfile = async (profileId, updatedData)=>{\n        setIsUpdating(true);\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.put(\"/freelancer/profile/\".concat(profileId), updatedData);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Success\",\n                description: \"Profile updated successfully\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const handleInputChange = (profileId, field, value)=>{\n        setEditingProfileData((prev)=>({\n                ...prev,\n                [profileId]: {\n                    ...prev[profileId],\n                    [field]: value\n                }\n            }));\n    };\n    const getProfileData = (profile)=>{\n        return editingProfileData[profile._id] || profile;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"Professional Profiles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>setIsCreateDialogOpen(true),\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Profile\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Loading profiles...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this) : profiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(profile._id),\n                                                    className: \"px-6 py-3 text-sm font-medium border-b-2 transition-colors duration-200 \".concat(activeTab === profile._id ? \"text-blue-600 border-blue-600 bg-blue-50\" : \"text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: profile.profileName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleDeleteProfile(profile._id);\n                                                                },\n                                                                className: \"ml-1 text-red-500 hover:text-red-700 opacity-70 hover:opacity-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, profile._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(activeTab === profile._id ? \"block\" : \"hidden\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-200 p-6 bg-black text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center pb-4 border-b border-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-semibold text-white\",\n                                                                        children: getProfileData(profile).profileName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleUpdateProfile(profile._id, getProfileData(profile)),\n                                                                                disabled: isUpdating,\n                                                                                className: \"flex items-center gap-2 bg-white text-black hover:bg-gray-200\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 367,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    isUpdating ? \"Updating...\" : \"Update\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"destructive\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleDeleteProfile(profile._id),\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 378,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Delete\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"profileName-\".concat(profile._id),\n                                                                                        className: \"text-white\",\n                                                                                        children: \"Profile Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"profileName-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).profileName || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"profileName\", e.target.value),\n                                                                                        placeholder: \"e.g., Frontend Developer\",\n                                                                                        className: \"bg-gray-800 text-white border-gray-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 394,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"hourlyRate-\".concat(profile._id),\n                                                                                        className: \"text-white\",\n                                                                                        children: \"Hourly Rate ($)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 411,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"hourlyRate-\".concat(profile._id),\n                                                                                        type: \"number\",\n                                                                                        value: getProfileData(profile).hourlyRate || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"hourlyRate\", parseFloat(e.target.value) || 0),\n                                                                                        placeholder: \"50\",\n                                                                                        className: \"bg-gray-800 text-white border-gray-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 417,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                htmlFor: \"description-\".concat(profile._id),\n                                                                                className: \"text-white\",\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                                                id: \"description-\".concat(profile._id),\n                                                                                value: getProfileData(profile).description || \"\",\n                                                                                onChange: (e)=>handleInputChange(profile._id, \"description\", e.target.value),\n                                                                                placeholder: \"Describe your expertise and experience...\",\n                                                                                rows: 4,\n                                                                                className: \"bg-gray-800 text-white border-gray-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 444,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_14__.Separator, {\n                                                                        className: \"bg-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        className: \"text-white\",\n                                                                                        children: \"Skills\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectTrigger, {\n                                                                                                className: \"bg-gray-800 text-white border-gray-600\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectValue, {\n                                                                                                    placeholder: \"Select skills\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                    lineNumber: 470,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 469,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectContent, {\n                                                                                                className: \"bg-gray-800 text-white border-gray-600\",\n                                                                                                children: skillsOptions.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                                        value: skill._id || skill.id,\n                                                                                                        className: \"hover:bg-gray-700\",\n                                                                                                        children: skill.label || skill.name || skill.skillName\n                                                                                                    }, skill._id || skill.id, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 474,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 472,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 468,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                                                        children: profile.skills && profile.skills.length > 0 ? profile.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                className: \"bg-blue-600 text-white\",\n                                                                                                children: [\n                                                                                                    typeof skill === \"string\" ? skill : skill.label || skill.name || skill.skillName,\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"h-3 w-3 ml-1 cursor-pointer hover:text-red-300\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 501,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, index, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 491,\n                                                                                                columnNumber: 41\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-gray-400 text-sm\",\n                                                                                            children: \"No skills selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                            lineNumber: 506,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 486,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 466,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        className: \"text-white\",\n                                                                                        children: \"Domains\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 513,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectTrigger, {\n                                                                                                className: \"bg-gray-800 text-white border-gray-600\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectValue, {\n                                                                                                    placeholder: \"Select domains\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                    lineNumber: 516,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 515,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectContent, {\n                                                                                                className: \"bg-gray-800 text-white border-gray-600\",\n                                                                                                children: domainsOptions.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                                        value: domain._id || domain.id,\n                                                                                                        className: \"hover:bg-gray-700\",\n                                                                                                        children: domain.label || domain.name || domain.domainName\n                                                                                                    }, domain._id || domain.id, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 520,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 518,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 514,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                                                        children: profile.domains && profile.domains.length > 0 ? profile.domains.map((domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                className: \"bg-green-600 text-white\",\n                                                                                                children: [\n                                                                                                    typeof domain === \"string\" ? domain : domain.label || domain.name || domain.domainName,\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"h-3 w-3 ml-1 cursor-pointer hover:text-red-300\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 547,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, index, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 537,\n                                                                                                columnNumber: 41\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-gray-400 text-sm\",\n                                                                                            children: \"No domains selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                            lineNumber: 552,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 532,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_14__.Separator, {\n                                                                        className: \"bg-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"githubLink-\".concat(profile._id),\n                                                                                        className: \"text-white\",\n                                                                                        children: \"GitHub Link\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 565,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"githubLink-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).githubLink || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"githubLink\", e.target.value),\n                                                                                        placeholder: \"https://github.com/username\",\n                                                                                        className: \"bg-gray-800 text-white border-gray-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 571,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"linkedinLink-\".concat(profile._id),\n                                                                                        className: \"text-white\",\n                                                                                        children: \"LinkedIn Link\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 588,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"linkedinLink-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).linkedinLink || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"linkedinLink\", e.target.value),\n                                                                                        placeholder: \"https://linkedin.com/in/username\",\n                                                                                        className: \"bg-gray-800 text-white border-gray-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 594,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"personalWebsite-\".concat(profile._id),\n                                                                                        className: \"text-white\",\n                                                                                        children: \"Personal Website\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 614,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"personalWebsite-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).personalWebsite || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"personalWebsite\", e.target.value),\n                                                                                        placeholder: \"https://yourwebsite.com\",\n                                                                                        className: \"bg-gray-800 text-white border-gray-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 620,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"availability-\".concat(profile._id),\n                                                                                        className: \"text-white\",\n                                                                                        children: \"Availability\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 638,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                                                        value: getProfileData(profile).availability || \"FREELANCE\",\n                                                                                        onValueChange: (value)=>handleInputChange(profile._id, \"availability\", value),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectTrigger, {\n                                                                                                className: \"bg-gray-800 text-white border-gray-600\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectValue, {\n                                                                                                    placeholder: \"Select availability\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                    lineNumber: 658,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 657,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectContent, {\n                                                                                                className: \"bg-gray-800 text-white border-gray-600\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                                        value: \"FULL_TIME\",\n                                                                                                        className: \"hover:bg-gray-700\",\n                                                                                                        children: \"Full Time\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 661,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                                        value: \"PART_TIME\",\n                                                                                                        className: \"hover:bg-gray-700\",\n                                                                                                        children: \"Part Time\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 667,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                                        value: \"CONTRACT\",\n                                                                                                        className: \"hover:bg-gray-700\",\n                                                                                                        children: \"Contract\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 673,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                                        value: \"FREELANCE\",\n                                                                                                        className: \"hover:bg-gray-700\",\n                                                                                                        children: \"Freelance\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 679,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 660,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 644,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 637,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_14__.Separator, {\n                                                                        className: \"bg-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>handleEditProfile(profile),\n                                                                                className: \"flex items-center gap-2 bg-gray-800 text-white border-gray-600 hover:bg-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 699,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Add Projects\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 694,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>handleEditProfile(profile),\n                                                                                className: \"flex items-center gap-2 bg-gray-800 text-white border-gray-600 hover:bg-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 707,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Add Experience\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>handleEditProfile(profile),\n                                                                                className: \"flex items-center gap-2 bg-gray-800 text-white border-gray-600 hover:bg-gray-700\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 715,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Add Education\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 710,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 21\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: isCreateDialogOpen,\n                onOpenChange: setIsCreateDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Create New Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Enter a name and description for your new professional profile.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profile Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            placeholder: \"e.g., Frontend Developer, Backend Engineer\",\n                                            value: newProfileName,\n                                            onChange: (e)=>setNewProfileName(e.target.value),\n                                            className: \"mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Description (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            placeholder: \"Describe your expertise and experience in this area... (minimum 10 characters if provided)\",\n                                            value: newProfileDescription,\n                                            onChange: (e)=>setNewProfileDescription(e.target.value),\n                                            className: \"mt-1\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-1\",\n                                            children: \"If left empty, a default description will be generated.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setIsCreateDialogOpen(false);\n                                        setNewProfileName(\"\");\n                                        setNewProfileDescription(\"\");\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleCreateProfile,\n                                    children: \"Create Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 731,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 782,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 793,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 792,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"Xg4PYVmsjYGlOwE/OrqDKX35IgU=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_16__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});