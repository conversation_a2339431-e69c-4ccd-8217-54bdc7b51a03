import { Service } from 'fastify-decorators';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { BaseDAO } from '../common/base.dao';
import { ReportModel, IReport } from '../models/report.entity';
import { fetchDataWithQueries } from '../common/utils';

@Service()
export class ReportDAO {

  constructor() {
  }

  // Create a new report
  async createReport(data: Partial<IReport>) {
    try {
      const report = await ReportModel.create({
        _id: uuidv4(),
        ...data,
        status: "OPEN", // Assuming default status
        createdAt: new Date(),
      });
      return report;
    } catch (error: any) {
      throw new Error(`Failed to create report: ${error.message}`);
    }
  }

  // Get reports with filters (paginated)
  async getReports(
    filters: Record<string, any>,
    page: string = '1',
    limit: string = '10',
  ) {
    try {
      return await fetchDataWithQueries(ReportModel, filters, page, limit);
    } catch (error: any) {
      throw new Error(`Failed to fetch reports: ${error.message}`);
    }
  }

  // Find report by ID
  async findReportById(reportId: string) {
    try {
      return await ReportModel.findById(reportId);
    } catch (error: any) {
      throw new Error(`Failed to find report by ID: ${error.message}`);
    }
  }

  // Update report by ID
  async updateReport(reportId: string, update: Partial<IReport>) {
    try {
      return await ReportModel.findByIdAndUpdate({ _id: reportId }, update, {
        new: true,
      });
    } catch (error: any) {
      throw new Error(`Failed to update report: ${error.message}`);
    }
  }

  // Delete report by ID
  async deleteReport(reportId: string) {
    try {
      return await ReportModel.findByIdAndDelete(reportId);
    } catch (error: any) {
      throw new Error(`Failed to delete report: ${error.message}`);
    }
  }

  // Count total reports
  async countReports(): Promise<number> {
    try {
      return ReportModel.countDocuments();
    } catch (error: any) {
      throw new Error(`Failed to count reports: ${error.message}`);
    }
  }

  // Get all reports for admin (including all statuses)
  async getAllReportsAdmin() {
    try {
      return await ReportModel.find();
    } catch (error: any) {
      throw new Error(`Failed to fetch reports for admin: ${error.message}`);
    }
  }
}
