"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dialogs/addEditProfileDialog */ \"(app-pages-browser)/./src/components/dialogs/addEditProfileDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProfileName, setNewProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newProfileDescription, setNewProfileDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfile, setEditingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            const profilesData = response.data.data || [];\n            setProfiles(profilesData);\n            // Set the first profile as active tab, or empty string if no profiles\n            if (profilesData.length > 0 && !activeTab && profilesData[0]._id) {\n                setActiveTab(profilesData[0]._id);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCreateProfile = async ()=>{\n        if (!newProfileName.trim()) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const description = newProfileDescription.trim() || \"Professional profile for \".concat(newProfileName.trim(), \". This profile showcases my skills and experience in this domain.\");\n        if (description.length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.post(\"/freelancer/profile\", {\n                profileName: newProfileName.trim(),\n                description: description,\n                skills: [],\n                domains: [],\n                projects: [],\n                experiences: [],\n                education: [],\n                portfolioLinks: []\n            });\n            const newProfile = response.data.data;\n            setProfiles([\n                ...profiles,\n                newProfile\n            ]);\n            if (newProfile._id) {\n                setActiveTab(newProfile._id);\n            }\n            setNewProfileName(\"\");\n            setNewProfileDescription(\"\");\n            setIsCreateDialogOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Success\",\n                description: \"Profile created successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error creating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to create profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            // If deleting the active tab, switch to another tab\n            if (activeTab === profileToDelete) {\n                const remainingProfiles = profiles.filter((p)=>p._id !== profileToDelete);\n                setActiveTab(remainingProfiles.length > 0 && remainingProfiles[0]._id ? remainingProfiles[0]._id : \"\");\n            }\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsEditDialogOpen(true);\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n        setIsEditDialogOpen(false);\n        setEditingProfile(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"Professional Profiles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>setIsCreateDialogOpen(true),\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Profile\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Loading profiles...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this) : profiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                                    value: activeTab,\n                                    onValueChange: setActiveTab,\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsList, {\n                                            className: \"grid w-full grid-cols-auto\",\n                                            children: profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                                    value: profile._id,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        profile.profileName,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleDeleteProfile(profile._id);\n                                                            },\n                                                            className: \"ml-1 text-red-500 hover:text-red-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, profile._id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                                                value: profile._id,\n                                                className: \"mt-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl font-semibold\",\n                                                                    children: profile.profileName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleEditProfile(profile),\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Edit Profile\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Profile form content coming soon...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: isCreateDialogOpen,\n                onOpenChange: setIsCreateDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Create New Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Enter a name and description for your new professional profile.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profile Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            placeholder: \"e.g., Frontend Developer, Backend Engineer\",\n                                            value: newProfileName,\n                                            onChange: (e)=>setNewProfileName(e.target.value),\n                                            className: \"mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Description (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            placeholder: \"Describe your expertise and experience in this area... (minimum 10 characters if provided)\",\n                                            value: newProfileDescription,\n                                            onChange: (e)=>setNewProfileDescription(e.target.value),\n                                            className: \"mt-1\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-1\",\n                                            children: \"If left empty, a default description will be generated.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setIsCreateDialogOpen(false);\n                                        setNewProfileName(\"\");\n                                        setNewProfileDescription(\"\");\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleCreateProfile,\n                                    children: \"Create Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"3QQnY87QQSe6P/Y72OUm37HJfh8=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});