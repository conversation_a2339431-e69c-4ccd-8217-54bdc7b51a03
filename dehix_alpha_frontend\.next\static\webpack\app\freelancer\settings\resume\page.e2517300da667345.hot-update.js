"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/resume/page",{

/***/ "(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx":
/*!***************************************************************!*\
  !*** ./src/config/menuItems/freelancer/settingsMenuItems.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   menuItemsBottom: function() { return /* binding */ menuItemsBottom; },\n/* harmony export */   menuItemsTop: function() { return /* binding */ menuItemsTop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Briefcase,HomeIcon,ImagePlus,Package,User,UserCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image-plus.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\nconst menuItemsTop = [\n    {\n        href: \"#\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            src: \"/dehix.png\" // Path to your image in the public folder\n            ,\n            alt: \"Icon\",\n            width: 16,\n            height: 16,\n            className: \"transition-all group-hover:scale-110 invert dark:invert-0\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined),\n        label: \"Dehix\"\n    },\n    {\n        href: \"/dashboard/freelancer\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 30,\n            columnNumber: 11\n        }, undefined),\n        label: \"Home\"\n    },\n    {\n        href: \"/freelancer/settings/personal-info\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 35,\n            columnNumber: 11\n        }, undefined),\n        label: \"Personal Info\"\n    },\n    {\n        href: \"/freelancer/settings/professional-info\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 40,\n            columnNumber: 11\n        }, undefined),\n        label: \"Professional Info\"\n    },\n    {\n        href: \"/freelancer/settings/projects\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 45,\n            columnNumber: 11\n        }, undefined),\n        label: \"Projects\"\n    },\n    {\n        href: \"/freelancer/settings/education-info\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 50,\n            columnNumber: 11\n        }, undefined),\n        label: \"Education\"\n    },\n    {\n        href: \"/freelancer/settings/profiles\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 55,\n            columnNumber: 11\n        }, undefined),\n        label: \"Profiles\"\n    },\n    {\n        href: \"/freelancer/settings/resume\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Briefcase_HomeIcon_ImagePlus_Package_User_UserCheck_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\config\\\\menuItems\\\\freelancer\\\\settingsMenuItems.tsx\",\n            lineNumber: 60,\n            columnNumber: 11\n        }, undefined),\n        label: \"Portfolio\"\n    }\n];\nconst menuItemsBottom = [];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\n"));

/***/ })

});