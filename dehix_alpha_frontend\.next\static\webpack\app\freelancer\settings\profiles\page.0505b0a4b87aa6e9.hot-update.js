"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProfileName, setNewProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            setProfiles(response.data.data || []);\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddProfile = ()=>{\n        setEditingProfile(null);\n        setIsDialogOpen(true);\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsDialogOpen(true);\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleViewProfile = (profile)=>{\n        // TODO: Implement profile view modal or navigate to profile view page\n        console.log(\"View profile:\", profile);\n    };\n    const handleToggleStatus = async (profileId, isActive)=>{\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.patch(\"/freelancer/profile/\".concat(profileId, \"/toggle-status\"), {\n                isActive\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: isActive ? \"Profile Activated\" : \"Profile Deactivated\",\n                description: \"Profile has been \".concat(isActive ? \"activated\" : \"deactivated\", \".\")\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error updating profile status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Professional Profiles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        ...Array(3)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                            className: \"h-[400px] w-full\"\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddProfileCard, {\n                                            onClick: handleAddProfile\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FreelancerProfileCard, {\n                                                profile: profile,\n                                                onEdit: handleEditProfile,\n                                                onDelete: handleDeleteProfile,\n                                                onView: handleViewProfile,\n                                                onToggleStatus: handleToggleStatus\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                !isLoading && profiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles yet\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddEditProfileDialog, {\n                open: isDialogOpen,\n                onOpenChange: setIsDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"ln+yNbEB7Ts8HewmHJBm3Z+eXR8=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_9__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});