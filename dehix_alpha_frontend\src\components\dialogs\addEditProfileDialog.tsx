import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Plus, X, RefreshCw, CheckSquare, Square } from 'lucide-react';

interface Skill {
  _id?: string;
  name: string;
  level?: string;
  experience?: string;
}

interface Domain {
  _id?: string;
  name: string;
  level?: string;
}

interface Project {
  _id?: string;
  projectName: string;
  description: string;
  role: string;
  start: string;
  end: string;
  techUsed: string[];
  githubLink?: string;
  projectType?: string;
  verified?: boolean;
}

interface ProfessionalExperience {
  _id?: string;
  company: string;
  jobTitle: string;
  workDescription: string;
  workFrom: string;
  workTo: string;
  referencePersonName?: string;
  referencePersonContact?: string;
  githubRepoLink?: string;
}

interface Education {
  _id?: string;
  degree: string;
  universityName: string;
  fieldOfStudy: string;
  startDate: string;
  endDate: string;
  grade: string;
}

interface FreelancerProfile {
  _id?: string;
  profileName: string;
  description: string;
  skills: Skill[];
  domains: Domain[];
  projects: Project[];
  experiences: ProfessionalExperience[];
  education: Education[];
  portfolioLinks?: string[];
  githubLink?: string;
  linkedinLink?: string;
  personalWebsite?: string;
  hourlyRate?: number;
  availability?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'FREELANCE';
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { axiosInstance } from '@/lib/axiosinstance';
import { toast } from '@/components/ui/use-toast';

const profileFormSchema = z.object({
  profileName: z
    .string()
    .min(1, 'Profile name is required')
    .max(100, 'Profile name must be less than 100 characters'),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(500, 'Description must be less than 500 characters'),
  githubLink: z.string().url('Invalid URL').optional().or(z.literal('')),
  linkedinLink: z.string().url('Invalid URL').optional().or(z.literal('')),
  personalWebsite: z.string().url('Invalid URL').optional().or(z.literal('')),
  hourlyRate: z.string().optional(),
  availability: z.enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE']),
});

interface AddEditProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  profile?: FreelancerProfile | null;
  onProfileSaved: () => void;
  freelancerId: string;
}

interface SkillOption {
  _id: string;
  name: string;
}

interface DomainOption {
  _id: string;
  name: string;
}

interface ProjectOption {
  _id: string;
  projectName: string;
}

interface ExperienceOption {
  _id: string;
  company: string;
  jobTitle: string;
}

interface EducationOption {
  _id: string;
  degree: string;
  universityName: string;
}

const AddEditProfileDialog: React.FC<AddEditProfileDialogProps> = ({
  open,
  onOpenChange,
  profile,
  onProfileSaved,
  freelancerId,
}) => {
  const [loading, setLoading] = useState(false);
  const [skillOptions, setSkillOptions] = useState<SkillOption[]>([]);
  const [domainOptions, setDomainOptions] = useState<DomainOption[]>([]);
  const [projectOptions, setProjectOptions] = useState<ProjectOption[]>([]);
  const [experienceOptions, setExperienceOptions] = useState<
    ExperienceOption[]
  >([]);
  const [educationOptions, setEducationOptions] = useState<EducationOption[]>(
    [],
  );

  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedDomains, setSelectedDomains] = useState<string[]>([]);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [selectedExperiences, setSelectedExperiences] = useState<string[]>([]);
  const [selectedEducation, setSelectedEducation] = useState<string[]>([]);
  const [portfolioLinks, setPortfolioLinks] = useState<string[]>(['']);

  const form = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      profileName: '',
      description: '',
      githubLink: '',
      linkedinLink: '',
      personalWebsite: '',
      hourlyRate: '',
      availability: 'FREELANCE',
    },
  });

  // Fetch available options
  useEffect(() => {
    if (open) {
      fetchOptions();
    }
  }, [open, freelancerId]);

  // Populate form when editing
  useEffect(() => {
    if (profile && open) {
      form.reset({
        profileName: profile.profileName,
        description: profile.description,
        githubLink: profile.githubLink || '',
        linkedinLink: profile.linkedinLink || '',
        personalWebsite: profile.personalWebsite || '',
        hourlyRate: profile.hourlyRate?.toString() || '',
        availability: profile.availability || 'FREELANCE',
      });

      setSelectedSkills(
        profile.skills?.map((s) => s._id!).filter(Boolean) || [],
      );
      setSelectedDomains(
        profile.domains?.map((d) => d._id!).filter(Boolean) || [],
      );
      setSelectedProjects(
        profile.projects?.map((p) => p._id!).filter(Boolean) || [],
      );
      setSelectedExperiences(
        profile.experiences?.map((e) => e._id!).filter(Boolean) || [],
      );
      setSelectedEducation(
        profile.education?.map((e) => e._id!).filter(Boolean) || [],
      );
      setPortfolioLinks(
        profile.portfolioLinks && profile.portfolioLinks.length > 0
          ? profile.portfolioLinks
          : [''],
      );
    } else if (open) {
      // Reset form for new profile
      form.reset();
      setSelectedSkills([]);
      setSelectedDomains([]);
      setSelectedProjects([]);
      setSelectedExperiences([]);
      setSelectedEducation([]);
      setPortfolioLinks(['']);
    }
  }, [profile, open, form]);

  const fetchOptions = async () => {
    try {
      const [skillsRes, domainsRes, projectsRes, experiencesRes, educationRes] =
        await Promise.all([
          axiosInstance.get(`/freelancer/${freelancerId}/skills`),
          axiosInstance.get(`/freelancer/${freelancerId}/domain`),
          axiosInstance.get(`/freelancer/${freelancerId}/myproject`),
          axiosInstance.get(`/freelancer/${freelancerId}/experience`),
          axiosInstance.get(`/freelancer/${freelancerId}/education`),
        ]);

      // Handle skills data
      const skillsData = skillsRes.data.data || [];
      const skillsArray = Array.isArray(skillsData)
        ? skillsData
        : Object.values(skillsData);
      setSkillOptions(skillsArray);

      // Handle domains data
      const domainsData = domainsRes.data.data || [];
      const domainsArray = Array.isArray(domainsData)
        ? domainsData
        : Object.values(domainsData);
      setDomainOptions(domainsArray);

      // Handle projects data
      const projectsData = projectsRes.data.data || [];
      const projectsArray = Array.isArray(projectsData)
        ? projectsData
        : Object.values(projectsData);
      setProjectOptions(projectsArray);

      // Handle experience data - convert to array if it's an object
      const experienceData = experiencesRes.data.data || [];
      const experienceArray = Array.isArray(experienceData)
        ? experienceData
        : Object.values(experienceData);
      setExperienceOptions(experienceArray);

      // Handle education data
      const educationData = educationRes.data.data || [];
      const educationArray = Array.isArray(educationData)
        ? educationData
        : Object.values(educationData);
      setEducationOptions(educationArray);

      // If creating a new profile (not editing), pre-select all items
      if (!profile) {
        setSelectedSkills(
          skillsArray.map((skill) => skill._id!).filter(Boolean),
        );
        setSelectedDomains(
          domainsArray.map((domain) => domain._id!).filter(Boolean),
        );
        setSelectedProjects(
          projectsArray.map((project) => project._id!).filter(Boolean),
        );
        setSelectedExperiences(
          experienceArray.map((experience) => experience._id!).filter(Boolean),
        );
        setSelectedEducation(
          educationArray.map((education) => education._id!).filter(Boolean),
        );
      }
    } catch (error) {
      console.error('Error fetching options:', error);
      toast({
        title: 'Error',
        description: 'Failed to load profile options',
        variant: 'destructive',
      });
    }
  };

  const addPortfolioLink = () => {
    setPortfolioLinks([...portfolioLinks, '']);
  };

  // Helper functions for select/deselect all
  const selectAllSkills = () => {
    setSelectedSkills(skillOptions.map((skill) => skill._id!).filter(Boolean));
  };

  const deselectAllSkills = () => {
    setSelectedSkills([]);
  };

  const selectAllDomains = () => {
    setSelectedDomains(
      domainOptions.map((domain) => domain._id!).filter(Boolean),
    );
  };

  const deselectAllDomains = () => {
    setSelectedDomains([]);
  };

  const selectAllProjects = () => {
    setSelectedProjects(
      projectOptions.map((project) => project._id!).filter(Boolean),
    );
  };

  const deselectAllProjects = () => {
    setSelectedProjects([]);
  };

  const selectAllExperiences = () => {
    setSelectedExperiences(
      experienceOptions.map((experience) => experience._id!).filter(Boolean),
    );
  };

  const deselectAllExperiences = () => {
    setSelectedExperiences([]);
  };

  const selectAllEducation = () => {
    setSelectedEducation(
      educationOptions.map((education) => education._id!).filter(Boolean),
    );
  };

  const deselectAllEducation = () => {
    setSelectedEducation([]);
  };

  const removePortfolioLink = (index: number) => {
    setPortfolioLinks(portfolioLinks.filter((_, i) => i !== index));
  };

  const updatePortfolioLink = (index: number, value: string) => {
    const updated = [...portfolioLinks];
    updated[index] = value;
    setPortfolioLinks(updated);
  };

  const toggleSelection = (
    id: string,
    selectedList: string[],
    setSelectedList: (list: string[]) => void,
  ) => {
    if (selectedList.includes(id)) {
      setSelectedList(selectedList.filter((item) => item !== id));
    } else {
      setSelectedList([...selectedList, id]);
    }
  };

  const onSubmit = async (data: z.infer<typeof profileFormSchema>) => {
    setLoading(true);
    try {
      const profileData = {
        ...data,
        hourlyRate: data.hourlyRate ? parseFloat(data.hourlyRate) : undefined,
        skills: selectedSkills,
        domains: selectedDomains,
        projects: selectedProjects,
        experiences: selectedExperiences,
        education: selectedEducation,
        portfolioLinks: portfolioLinks.filter((link) => link.trim() !== ''),
      };

      if (profile?._id) {
        await axiosInstance.put(
          `/freelancer/profile/${profile._id}`,
          profileData,
        );
        toast({
          title: 'Profile Updated',
          description: 'Your profile has been successfully updated.',
        });
      } else {
        await axiosInstance.post(`/freelancer/profile`, profileData);
        toast({
          title: 'Profile Created',
          description: 'Your new profile has been successfully created.',
        });
      }

      onProfileSaved();
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to save profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {profile ? 'Edit Profile' : 'Create New Profile'}
          </DialogTitle>
          <DialogDescription>
            {profile
              ? 'Update your professional profile information.'
              : 'Create a new professional profile to showcase your skills and experience.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="profileName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Profile Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Frontend Developer, Backend Engineer"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Give your profile a descriptive name
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="availability"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Availability</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select availability" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="FULL_TIME">Full Time</SelectItem>
                        <SelectItem value="PART_TIME">Part Time</SelectItem>
                        <SelectItem value="CONTRACT">Contract</SelectItem>
                        <SelectItem value="FREELANCE">Freelance</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your expertise, experience, and what makes you unique..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a compelling description of your professional
                    background
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Links and Rate */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="hourlyRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hourly Rate (USD)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="50" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your preferred hourly rate
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="githubLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>GitHub Profile</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://github.com/username"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="linkedinLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>LinkedIn Profile</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://linkedin.com/in/username"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="personalWebsite"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Personal Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://yourwebsite.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Portfolio Links */}
            <div>
              <FormLabel>Portfolio Links</FormLabel>
              <FormDescription className="mb-3">
                Add links to your portfolio projects or work samples
              </FormDescription>
              {portfolioLinks.map((link, index) => (
                <div key={index} className="flex gap-2 mb-2">
                  <Input
                    placeholder="https://portfolio-project.com"
                    value={link}
                    onChange={(e) => updatePortfolioLink(index, e.target.value)}
                  />
                  {portfolioLinks.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removePortfolioLink(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addPortfolioLink}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Portfolio Link
              </Button>
            </div>

            {/* Skills Selection */}
            <div>
              <FormLabel>Skills</FormLabel>
              <FormDescription className="mb-3">
                {profile
                  ? 'Select the skills relevant to this profile'
                  : "All your skills are pre-selected. Click to deselect any you don't want in this profile"}
              </FormDescription>
              <div className="mb-3 flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open('/freelancer/settings/skills', '_blank')
                  }
                  className="text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Skill
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={fetchOptions}
                  className="text-sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectAllSkills}
                  className="text-sm"
                >
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={deselectAllSkills}
                  className="text-sm"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Deselect All
                </Button>
              </div>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {Array.isArray(skillOptions) &&
                    skillOptions.map((skill) => (
                      <div
                        key={skill._id}
                        className={`p-2 rounded cursor-pointer border ${
                          selectedSkills.includes(skill._id)
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-background hover:bg-muted'
                        }`}
                        onClick={() =>
                          toggleSelection(
                            skill._id,
                            selectedSkills,
                            setSelectedSkills,
                          )
                        }
                      >
                        <span className="text-sm">{skill.name}</span>
                      </div>
                    ))}
                </div>
              </div>
              {selectedSkills.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-1">
                  {selectedSkills.map((skillId) => {
                    const skill = skillOptions.find((s) => s._id === skillId);
                    return skill ? (
                      <Badge key={skillId} variant="secondary">
                        {skill.name}
                      </Badge>
                    ) : null;
                  })}
                </div>
              )}
            </div>

            {/* Domains Selection */}
            <div>
              <FormLabel>Domains</FormLabel>
              <FormDescription className="mb-3">
                {profile
                  ? 'Select the domains you work in'
                  : "All your domains are pre-selected. Click to deselect any you don't want in this profile"}
              </FormDescription>
              <div className="mb-3 flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open('/freelancer/settings/domains', '_blank')
                  }
                  className="text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Domain
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={fetchOptions}
                  className="text-sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectAllDomains}
                  className="text-sm"
                >
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={deselectAllDomains}
                  className="text-sm"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Deselect All
                </Button>
              </div>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {Array.isArray(domainOptions) &&
                    domainOptions.map((domain) => (
                      <div
                        key={domain._id}
                        className={`p-2 rounded cursor-pointer border ${
                          selectedDomains.includes(domain._id)
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-background hover:bg-muted'
                        }`}
                        onClick={() =>
                          toggleSelection(
                            domain._id,
                            selectedDomains,
                            setSelectedDomains,
                          )
                        }
                      >
                        <span className="text-sm">{domain.name}</span>
                      </div>
                    ))}
                </div>
              </div>
              {selectedDomains.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-1">
                  {selectedDomains.map((domainId) => {
                    const domain = domainOptions.find(
                      (d) => d._id === domainId,
                    );
                    return domain ? (
                      <Badge key={domainId} variant="secondary">
                        {domain.name}
                      </Badge>
                    ) : null;
                  })}
                </div>
              )}
            </div>

            {/* Projects Selection */}
            <div>
              <FormLabel>Projects</FormLabel>
              <FormDescription className="mb-3">
                {profile
                  ? 'Select projects to include in this profile'
                  : "All your projects are pre-selected. Click to deselect any you don't want in this profile"}
              </FormDescription>
              <div className="mb-3 flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open('/freelancer/settings/projects', '_blank')
                  }
                  className="text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Project
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={fetchOptions}
                  className="text-sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectAllProjects}
                  className="text-sm"
                >
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={deselectAllProjects}
                  className="text-sm"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Deselect All
                </Button>
              </div>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                <div className="space-y-2">
                  {Array.isArray(projectOptions) &&
                    projectOptions.map((project) => (
                      <div
                        key={project._id}
                        className={`p-2 rounded cursor-pointer border ${
                          selectedProjects.includes(project._id)
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-background hover:bg-muted'
                        }`}
                        onClick={() =>
                          toggleSelection(
                            project._id,
                            selectedProjects,
                            setSelectedProjects,
                          )
                        }
                      >
                        <span className="text-sm font-medium">
                          {project.projectName}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            {/* Experiences Selection */}
            <div>
              <FormLabel>Work Experience</FormLabel>
              <FormDescription className="mb-3">
                {profile
                  ? 'Select work experiences to include in this profile'
                  : "All your work experiences are pre-selected. Click to deselect any you don't want in this profile"}
              </FormDescription>
              <div className="mb-3 flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open('/freelancer/settings/experience', '_blank')
                  }
                  className="text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Experience
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={fetchOptions}
                  className="text-sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectAllExperiences}
                  className="text-sm"
                >
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={deselectAllExperiences}
                  className="text-sm"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Deselect All
                </Button>
              </div>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                <div className="space-y-2">
                  {Array.isArray(experienceOptions) &&
                  experienceOptions.length > 0 ? (
                    experienceOptions.map((experience) => (
                      <div
                        key={experience._id}
                        className={`p-2 rounded cursor-pointer border ${
                          selectedExperiences.includes(experience._id)
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-background hover:bg-muted'
                        }`}
                        onClick={() =>
                          toggleSelection(
                            experience._id,
                            selectedExperiences,
                            setSelectedExperiences,
                          )
                        }
                      >
                        <span className="text-sm font-medium">
                          {experience.jobTitle}
                        </span>
                        <span className="text-xs block text-muted-foreground">
                          {experience.company}
                        </span>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-4">
                      No work experience found
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Education Selection */}
            <div>
              <FormLabel>Education</FormLabel>
              <FormDescription className="mb-3">
                {profile
                  ? 'Select education to include in this profile'
                  : "All your education entries are pre-selected. Click to deselect any you don't want in this profile"}
              </FormDescription>
              <div className="mb-3 flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open('/freelancer/settings/education', '_blank')
                  }
                  className="text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Education
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={fetchOptions}
                  className="text-sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectAllEducation}
                  className="text-sm"
                >
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={deselectAllEducation}
                  className="text-sm"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Deselect All
                </Button>
              </div>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                <div className="space-y-2">
                  {Array.isArray(educationOptions) &&
                    educationOptions.map((education) => (
                      <div
                        key={education._id}
                        className={`p-2 rounded cursor-pointer border ${
                          selectedEducation.includes(education._id)
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-background hover:bg-muted'
                        }`}
                        onClick={() =>
                          toggleSelection(
                            education._id,
                            selectedEducation,
                            setSelectedEducation,
                          )
                        }
                      >
                        <span className="text-sm font-medium">
                          {education.degree}
                        </span>
                        <span className="text-xs block text-muted-foreground">
                          {education.universityName}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading
                  ? 'Saving...'
                  : profile
                    ? 'Update Profile'
                    : 'Create Profile'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddEditProfileDialog;
