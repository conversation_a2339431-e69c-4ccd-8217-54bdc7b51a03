"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dialogs/addEditProfileDialog */ \"(app-pages-browser)/./src/components/dialogs/addEditProfileDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProfileName, setNewProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newProfileDescription, setNewProfileDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfile, setEditingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            const profilesData = response.data.data || [];\n            setProfiles(profilesData);\n            // Set the first profile as active tab, or empty string if no profiles\n            if (profilesData.length > 0 && !activeTab && profilesData[0]._id) {\n                setActiveTab(profilesData[0]._id);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCreateProfile = async ()=>{\n        if (!newProfileName.trim()) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const description = newProfileDescription.trim() || \"Professional profile for \".concat(newProfileName.trim(), \". This profile showcases my skills and experience in this domain.\");\n        if (description.length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.post(\"/freelancer/profile\", {\n                profileName: newProfileName.trim(),\n                description: description,\n                skills: [],\n                domains: [],\n                projects: [],\n                experiences: [],\n                education: [],\n                portfolioLinks: []\n            });\n            const newProfile = response.data.data;\n            setProfiles([\n                ...profiles,\n                newProfile\n            ]);\n            if (newProfile._id) {\n                setActiveTab(newProfile._id);\n            }\n            setNewProfileName(\"\");\n            setNewProfileDescription(\"\");\n            setIsCreateDialogOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Success\",\n                description: \"Profile created successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error creating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to create profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            // If deleting the active tab, switch to another tab\n            if (activeTab === profileToDelete) {\n                const remainingProfiles = profiles.filter((p)=>p._id !== profileToDelete);\n                setActiveTab(remainingProfiles.length > 0 && remainingProfiles[0]._id ? remainingProfiles[0]._id : \"\");\n            }\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsEditDialogOpen(true);\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n        setIsEditDialogOpen(false);\n        setEditingProfile(null);\n    };\n    const handleToggleStatus = async (profileId, isActive)=>{\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.patch(\"/freelancer/profile/\".concat(profileId, \"/toggle-status\"), {\n                isActive\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: isActive ? \"Profile Activated\" : \"Profile Deactivated\",\n                description: \"Profile has been \".concat(isActive ? \"activated\" : \"deactivated\", \".\")\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error updating profile status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"Professional Profiles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>setIsCreateDialogOpen(true),\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Profile\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Loading profiles...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this) : profiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                                    value: activeTab,\n                                    onValueChange: setActiveTab,\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                                            className: \"grid w-full grid-cols-auto\",\n                                            children: profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                    value: profile._id,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        profile.profileName,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleDeleteProfile(profile._id);\n                                                            },\n                                                            className: \"ml-1 text-red-500 hover:text-red-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, profile._id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                                value: profile._id,\n                                                className: \"mt-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl font-semibold\",\n                                                                    children: profile.profileName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleEditProfile(profile),\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Edit Profile\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Profile form content coming soon...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: isCreateDialogOpen,\n                onOpenChange: setIsCreateDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                    children: \"Create New Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogDescription, {\n                                    children: \"Enter a name and description for your new professional profile.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profile Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            placeholder: \"e.g., Frontend Developer, Backend Engineer\",\n                                            value: newProfileName,\n                                            onChange: (e)=>setNewProfileName(e.target.value),\n                                            className: \"mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Description (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            placeholder: \"Describe your expertise and experience in this area... (minimum 10 characters if provided)\",\n                                            value: newProfileDescription,\n                                            onChange: (e)=>setNewProfileDescription(e.target.value),\n                                            className: \"mt-1\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-1\",\n                                            children: \"If left empty, a default description will be generated.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setIsCreateDialogOpen(false);\n                                        setNewProfileName(\"\");\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleCreateProfile,\n                                    children: \"Create Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"3QQnY87QQSe6P/Y72OUm37HJfh8=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});