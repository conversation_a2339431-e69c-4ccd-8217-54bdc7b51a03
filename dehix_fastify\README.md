# Dehix Fastify Backend

## Introduction

**Dehix Fastify Backend** is a high-performance, scalable backend service built using **Fastify**, a fast and lightweight Node.js framework. The project is designed to deliver RESTful APIs for managing application functionalities while ensuring speed, reliability, and low overhead.

This backend is integrated with **AWS Elastic Beanstalk** for seamless deployment and uses modern development practices like continuous integration and continuous deployment (CI/CD). The architecture is built to support dynamic scaling, ensuring performance under varying loads.

## Features

- **Fast and Lightweight APIs**: Built with Fastify, optimized for speed
- **Scalable Infrastructure**: Deployed on AWS Elastic Beanstalk with S3 for artifact storage
- **Efficient CI/CD Pipeline**: Automated build, artifact generation, and deployment using GitHub Actions
- **Environment-Specific Configurations**: Supports dev, staging, and production environments
- **TypeScript Support**: Full TypeScript implementation for type safety
- **Modular Architecture**: Well-organized code structure with separation of concerns

## Project Structure

```
dehix-fastify/
├── .husky/                 # Git hooks configuration
├── eb/                     # Elastic Beanstalk configuration
├── scripts/                # Build and deployment scripts
├── src/                    # Source code
│   ├── clients/            # External service clients (Firebase, MongoDB, etc.)
│   ├── common/             # Shared utilities, base classes, and helper functions
│   ├── constants/          # Application constants and API endpoint definitions
│   ├── controllers/        # Route controllers handling HTTP requests/responses
│   ├── dao/                # Data Access Objects for database operations
│   ├── database/           # Database configuration, DDL scripts, and queries
│   ├── middlewares/        # Custom middleware functions (auth, validation, etc.)
│   ├── models/             # Mongoose models and entity definitions
│   ├── schema/             # Fastify request/response schema definitions
│   │   └── v1/             # Version 1 API schemas
│   ├── services/           # Business logic services (admin.service.ts, etc.)
│   ├── types/              # TypeScript type definitions and interfaces
│   ├── zod-schema/         # Zod validation schemas for data validation
│   │   └── v1/             # Version 1 Zod schemas
│   ├── app.ts              # Main Fastify application setup
│   └── loader.ts           # Application loader and initialization
├── .eslintrc.json          # ESLint configuration
├── .gitignore              # Git ignore rules
├── package.json            # Node.js dependencies and scripts
├── package-lock.json       # Locked dependency versions
├── prettierignore          # Prettier ignore rules
├── requirements.txt        # Python requirements (for build scripts)
├── serverless.yml          # Serverless framework configuration
├── tsconfig.json           # TypeScript configuration
└── README.md               # Project documentation
```

### Detailed Folder Descriptions

#### `/src/clients/`
Contains external service clients and integrations:
- **Firebase Client**: Firebase authentication and Firestore operations
- **MongoDB Client**: MongoDB connection and client configuration
- **Other Third-party APIs**: External service integrations

#### `/src/common/`
Shared utilities and base classes used across the application:
- **Base Classes**: `BaseService`, `BaseDAO` for common functionality
- **Error Handling**: Custom error classes and error handling utilities
- **Helper Functions**: Utility functions used throughout the application
- **Shared Services**: Common services like logging, validation, etc.

#### `/src/constants/`
Application-wide constants and configuration:
- **API Endpoints**: All API route definitions and endpoint constants
- **Error Codes**: Standardized error codes and messages
- **Response Messages**: Consistent response message templates
- **Configuration Constants**: Application configuration values

#### `/src/controllers/`
Route controllers that handle HTTP requests and responses:
- Handle incoming HTTP requests
- Validate request data
- Call appropriate services
- Format and return responses
- Manage HTTP status codes

#### `/src/dao/` (Data Access Objects)
Database operation layer that provides abstraction over data persistence:
- **BaseDAO**: Abstract base class with common CRUD operations
- **Entity-specific DAOs**: Specialized database operations for each entity
- **Database Abstraction**: Isolates business logic from database implementation
- **Query Building**: Complex query construction and execution

#### `/src/database/`
Database configuration and SQL operations:
- **DDL Scripts**: Database schema creation and migration scripts
- **Queries**: Raw SQL queries and stored procedures
- **Connection Management**: Database connection configuration
- **Migration Scripts**: Database version control and updates

#### `/src/middlewares/`
Custom Fastify middleware functions:
- **Authentication Middleware**: JWT token validation and user authentication
- **Authorization Middleware**: Role-based access control
- **Validation Middleware**: Request/response validation
- **Logging Middleware**: Request/response logging
- **Error Handling Middleware**: Global error handling and formatting

#### `/src/models/`
Mongoose models and entity definitions:
- **Entity Models**: Mongoose schema definitions for database entities
- **Model Interfaces**: TypeScript interfaces for model structure
- **Schema Validation**: Mongoose schema validation rules
- **Relationships**: Model relationships and references

#### `/src/schema/v1/`
Fastify request/response schema definitions:
- **Request Schemas**: Validation schemas for incoming requests
- **Response Schemas**: Structure definitions for API responses
- **Parameter Schemas**: URL parameter and query parameter validation
- **Version Management**: API version-specific schema definitions

#### `/src/services/`
Business logic layer containing core application functionality:
- **Admin Service**: User management and admin operations
- **Authentication Service**: Login, registration, and token management
- **Business Logic**: Core application functionality and rules
- **External Integrations**: Service-level integrations with external APIs

#### `/src/types/`
TypeScript type definitions and interfaces:
- **Entity Types**: TypeScript interfaces for data entities
- **API Types**: Request/response type definitions
- **Utility Types**: Generic utility types and type helpers
- **Service Types**: Type definitions for service layer

#### `/src/zod-schema/v1/`
Zod validation schemas for runtime type checking:
- **Input Validation**: Schema validation for incoming data
- **Runtime Type Safety**: Zod schemas for type validation
- **Data Transformation**: Input data transformation and sanitization
- **Version-specific Schemas**: API version-specific validation rules

#### Core Files

##### `/src/app.ts`
Main Fastify application setup and configuration:
- Fastify instance creation and configuration
- Plugin registration and middleware setup
- Route registration and API versioning
- Error handling and logging configuration

##### `/src/loader.ts`
Application loader and initialization:
- Database connection initialization
- Service container setup
- Configuration loading and validation
- Application bootstrap process

## API Overview

### Base URLs

The base URL for the APIs depends on the deployment environment:

- **Development**: `http://localhost:3000`
- **Staging**: `https://devapi.dehix.org`
- **Production**: `https://api.dehix.org`

## Prerequisites

- **Node.js**: Version 18.x or higher
- **npm**: Version 8.x or higher
- **Python**: Version 3.x (for build scripts)
- **AWS CLI**: For deployment management
- **Git**: For version control

## Development Setup

### 1. Clone the Repository

```bash
git clone https://github.com/dehixorg/dehix-fastify.git
cd dehix-fastify
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Create environment-specific configuration files:

```bash
# Copy example environment file (if available)
cp .env.example .env

# Configure your environment variables
# Add your Firebase, AWS, and other service configurations
```

### 4. Start Development Server

```bash
npm run dev
```

The server will start on `http://localhost:3000` by default.

### 5. Available Scripts

### Setup Husky (optional for Git hooks)

```bash
npm install --save-dev husky
# OR
yarn add --dev husky
bash
Copy
Edit
npx husky install

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build the project for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues automatically
npm run format       # Format code with Prettier

# Testing (if applicable)
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
```

## Build and Deployment

### CI/CD Pipeline

The CI/CD pipeline automates the build and deployment process and is defined in `.github/workflows/deploy.yaml`.

#### Pipeline Flow

1. **Trigger**: The pipeline is triggered by push events on the `develop` branch
2. **Build Steps**:
   - **Checkout Code**: Pulls the latest code from the repository
   - **Install Node.js and Python**: Sets up the runtime environment for the build process
   - **Install Dependencies**: Runs `npm install` to resolve dependencies
   - **Build Script**: Executes `scripts/build.py` to prepare a deployable ZIP file
   - **Run Development Server** (optional): Installs and starts the development server for testing
3. **Deployment Steps**:
   - **AWS Credentials**: Configures AWS access using GitHub secrets
   - **Upload to S3**: Uploads the ZIP file to the S3 bucket (`beanstalk-fastify-builds`)
   - **Elastic Beanstalk**:
     - Creates a new application version
     - Updates the environment with the new version

### Configuration Details

#### Required GitHub Secrets

- `AWS_ACCESS_KEY`: AWS Access Key ID
- `AWS_SECRET_KEY`: AWS Secret Access Key
- `AWS_REGION`: AWS Region (e.g., `ap-south-1`)
- `FIREBASE_DEV_JSON`: Firebase configuration JSON

#### AWS Elastic Beanstalk Configuration

- **Application Name**: `dehix-fastify`
- **Environment Name**: `dehix-fastify-env`
- **Platform**: Node.js

#### S3 Configuration

- **Bucket Name**: `beanstalk-fastify-builds`
- **Purpose**: Stores deployment artifacts

## Build Script

The `scripts/build.py` script prepares the deployment package by:

- Installing production dependencies (`npm install`)
- Building the project using `npm run build`
- Copying necessary configuration files to the build directory
- Creating a ZIP file for deployment
- Cleaning up temporary files

## Environment Variables

### Required Environment Variables

```bash
# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# Application Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Database Configuration (if applicable)
DATABASE_URL=your_database_connection_string
```

## Architecture Overview

The application follows a layered architecture pattern with clear separation of concerns:

### Data Flow Architecture

```
HTTP Request → Controller → Service → DAO → Database
HTTP Response ← Controller ← Service ← DAO ← Database
```

### Layer Responsibilities

#### 1. **Controllers Layer**
- Handle HTTP requests and responses
- Input validation and sanitization
- Route parameter extraction
- Response formatting and status codes
- Error handling at the HTTP level

#### 2. **Services Layer** 
Contains business logic and orchestrates operations:
- Business rule implementation
- Transaction coordination
- Integration with external services (Firebase)
- Error handling and rollback mechanisms
- Data transformation and processing

#### 3. **DAO (Data Access Objects) Layer**
Handles database operations with generic CRUD functionality:
- Abstract BaseDAO class with common database operations
- Generic methods for create, update, delete operations
- Database abstraction and query optimization
- Connection management and transaction support

#### 4. **Models Layer**
- Mongoose schema definitions for MongoDB collections
- Data validation rules and constraints
- Entity relationships and references
- Virtual properties and computed fields

#### 5. **Middleware Layer**
- Authentication and authorization checks
- Request/response logging and monitoring
- Input validation and sanitization
- Global error handling and formatting
- Rate limiting and security controls

### Key Design Patterns

#### Dependency Injection
- Uses `fastify-decorators` for service container management
- `@Service()` decorator for automatic service registration
- `@Inject()` decorator for dependency resolution
- Promotes loose coupling and enhanced testability

#### Repository Pattern (DAO)
- Abstracts database operations from business logic
- Provides consistent interface for data access across entities
- Enables easy testing with mock implementations
- Supports multiple database backends through abstraction

#### Error Handling Strategy
- Custom error classes for different error types and scenarios
- Centralized error handling in controllers with consistent responses
- Rollback mechanisms for failed operations (e.g., Firebase user creation)
- Structured error codes and messages for API consumers

### External Integrations

#### Firebase Integration
- User authentication and session management
- Custom claims for role-based access control
- User lifecycle management (creation, updates, deletion)
- Real-time database operations when needed

#### MongoDB Integration
- Mongoose ODM for schema-based data modeling
- Generic DAO pattern for standardized CRUD operations
- Connection pooling and performance optimization
- Support for transactions and complex queries

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style

- Follow the existing ESLint and Prettier configurations
- Write TypeScript with proper type definitions
- Add appropriate JSDoc comments for public APIs
- Maintain consistent naming conventions

## Monitoring and Logging

The application includes:

- Structured logging with appropriate log levels
- Health check endpoints for monitoring
- Error handling and reporting
- Performance metrics tracking

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Kill process using port 3000
   lsof -ti:3000 | xargs kill -9
   ```

2. **AWS Deployment Issues**
   - Verify AWS credentials are correctly configured
   - Check S3 bucket permissions
   - Ensure Elastic Beanstalk environment is running

3. **Build Failures**
   - Clear node_modules and reinstall dependencies
   - Check TypeScript compilation errors
   - Verify all environment variables are set

## Support

For support and questions:

- Create an issue in the GitHub repository
- Contact the development team
- Check the project documentation

## License

This project is licensed under the [MIT License](LICENSE) - see the LICENSE file for details.

---

## Acknowledgments

- Built with [Fastify](https://www.fastify.io/)
- Deployed on [AWS Elastic Beanstalk](https://aws.amazon.com/elasticbeanstalk/)
- CI/CD powered by [GitHub Actions](https://github.com/features/actions)