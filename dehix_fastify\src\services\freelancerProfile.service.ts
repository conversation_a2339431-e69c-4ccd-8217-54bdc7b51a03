import { Inject, Service } from "fastify-decorators";

import { FreelancerProfileDAO } from "../dao/freelancerProfile.dao";
import { FreelancerDAO } from "../dao/freelancer.dao";
import {
  IFreelancerProfile,
  AvailabilityEnum,
} from "../models/freelancerProfile.entity";

import {
  BadRequestError,
  NotFoundError,
  ConflictError,
} from "../common/errors";
import { ERROR_CODES, RESPONSE_MESSAGE } from "../common/constants";

export interface CreateProfileRequest {
  profileName: string;
  description: string;
  skills?: string[];
  domains?: string[];
  projects?: string[];
  experiences?: string[];
  education?: string[];
  portfolioLinks?: string[];
  githubLink?: string;
  linkedinLink?: string;
  personalWebsite?: string;
  hourlyRate?: number;
  availability?: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "FREELANCE";
}

export interface UpdateProfileRequest extends Partial<CreateProfileRequest> {
  isActive?: boolean;
}

@Service()
export class FreelancerProfileService {
  @Inject(FreelancerProfileDAO)
  freelancerProfileDAO!: FreelancerProfileDAO;

  @Inject(FreelancerDAO)
  freelancerDAO!: FreelancerDAO;

  async createProfile(
    freelancerId: string,
    profileData: CreateProfileRequest
  ): Promise<IFreelancerProfile> {
    // Validate freelancer exists
    const freelancer =
      await this.freelancerDAO.findFreelancerById(freelancerId);
    if (!freelancer) {
      throw new NotFoundError(
        RESPONSE_MESSAGE.FREELANCER_NOT_FOUND,
        ERROR_CODES.FREELANCER_NOT_FOUND
      );
    }

    // Check if profile name already exists for this freelancer
    const existingProfile =
      await this.freelancerProfileDAO.findProfileByFreelancerIdAndName(
        freelancerId,
        profileData.profileName
      );

    console.log("Checking if profile name already exists...", existingProfile);

    if (existingProfile) {
      throw new ConflictError(
        "Profile name already exists",
        "PROFILE_NAME_EXISTS"
      );
    }

    console.log("Profile name is unique, proceeding to create profile...");

    // Validate profile data
    console.log("About to validate profile data...");
    this.validateProfileData(profileData);
    console.log("Profile data validation completed successfully!");

    // Create profile
    console.log("Creating profile object...");
    const profileToCreate: Partial<IFreelancerProfile> = {
      freelancerId,
      profileName: profileData.profileName,
      description: profileData.description,
      skills: profileData.skills || [],
      domains: profileData.domains || [],
      projects: profileData.projects || [],
      experiences: profileData.experiences || [],
      education: profileData.education || [],
      portfolioLinks: profileData.portfolioLinks,
      githubLink: profileData.githubLink,
      linkedinLink: profileData.linkedinLink,
      personalWebsite: profileData.personalWebsite,
      hourlyRate: profileData.hourlyRate,
      availability: profileData.availability as AvailabilityEnum,
      isActive: true,
    };

    console.log("Profile data that is going to be created : ", profileToCreate);

    const profile =
      await this.freelancerProfileDAO.createProfile(profileToCreate);

    return profile;
  }

  async getProfileById(profileId: string): Promise<IFreelancerProfile> {
    const profile = await this.freelancerProfileDAO.findProfileById(profileId);
    if (!profile) {
      throw new NotFoundError("Profile not found", "PROFILE_NOT_FOUND");
    }

    return profile;
  }

  async getProfilesByFreelancerId(
    freelancerId: string
  ): Promise<IFreelancerProfile[]> {
    // Validate freelancer exists
    const freelancer =
      await this.freelancerDAO.findFreelancerById(freelancerId);
    if (!freelancer) {
      throw new NotFoundError(
        RESPONSE_MESSAGE.FREELANCER_NOT_FOUND,
        ERROR_CODES.FREELANCER_NOT_FOUND
      );
    }

    return await this.freelancerProfileDAO.findProfilesByFreelancerId(
      freelancerId
    );
  }

  async getActiveProfilesByFreelancerId(
    freelancerId: string
  ): Promise<IFreelancerProfile[]> {
    // Validate freelancer exists
    const freelancer =
      await this.freelancerDAO.findFreelancerById(freelancerId);
    if (!freelancer) {
      throw new NotFoundError(
        RESPONSE_MESSAGE.FREELANCER_NOT_FOUND,
        ERROR_CODES.FREELANCER_NOT_FOUND
      );
    }

    return await this.freelancerProfileDAO.findActiveProfilesByFreelancerId(
      freelancerId
    );
  }

  async updateProfile(
    profileId: string,
    freelancerId: string,
    updateData: UpdateProfileRequest
  ): Promise<IFreelancerProfile> {
    // Get existing profile and validate ownership
    const existingProfile =
      await this.freelancerProfileDAO.findProfileById(profileId);
    if (!existingProfile) {
      throw new NotFoundError("Profile not found", "PROFILE_NOT_FOUND");
    }

    if (existingProfile.freelancerId !== freelancerId) {
      throw new BadRequestError(
        "Unauthorized to update this profile",
        "UNAUTHORIZED_PROFILE_UPDATE"
      );
    }

    // Check if profile name is being changed and if it conflicts
    if (
      updateData.profileName &&
      updateData.profileName !== existingProfile.profileName
    ) {
      const conflictingProfile =
        await this.freelancerProfileDAO.findProfileByFreelancerIdAndName(
          freelancerId,
          updateData.profileName
        );
      if (conflictingProfile) {
        throw new ConflictError(
          "Profile name already exists",
          "PROFILE_NAME_EXISTS"
        );
      }
    }

    // Validate update data
    if (updateData.profileName || updateData.description) {
      this.validateProfileData(updateData as CreateProfileRequest);
    }

    // Convert updateData to match IFreelancerProfile interface
    const updateToApply: Partial<IFreelancerProfile> = {
      ...updateData,
      availability: updateData.availability as AvailabilityEnum,
    };

    const updatedProfile = await this.freelancerProfileDAO.updateProfile(
      profileId,
      updateToApply
    );
    if (!updatedProfile) {
      throw new NotFoundError("Profile not found", "PROFILE_NOT_FOUND");
    }

    return updatedProfile;
  }

  async deleteProfile(profileId: string, freelancerId: string): Promise<void> {
    // Get existing profile and validate ownership
    const existingProfile =
      await this.freelancerProfileDAO.findProfileById(profileId);
    if (!existingProfile) {
      throw new NotFoundError("Profile not found", "PROFILE_NOT_FOUND");
    }

    if (existingProfile.freelancerId !== freelancerId) {
      throw new BadRequestError(
        "Unauthorized to delete this profile",
        "UNAUTHORIZED_PROFILE_DELETE"
      );
    }

    await this.freelancerProfileDAO.deleteProfile(profileId);
  }

  async toggleProfileStatus(
    profileId: string,
    freelancerId: string,
    isActive: boolean
  ): Promise<IFreelancerProfile> {
    // Get existing profile and validate ownership
    const existingProfile =
      await this.freelancerProfileDAO.findProfileById(profileId);
    if (!existingProfile) {
      throw new NotFoundError("Profile not found", "PROFILE_NOT_FOUND");
    }

    if (existingProfile.freelancerId !== freelancerId) {
      throw new BadRequestError(
        "Unauthorized to update this profile",
        "UNAUTHORIZED_PROFILE_UPDATE"
      );
    }

    const updatedProfile = await this.freelancerProfileDAO.toggleProfileStatus(
      profileId,
      isActive
    );
    if (!updatedProfile) {
      throw new NotFoundError("Profile not found", "PROFILE_NOT_FOUND");
    }

    return updatedProfile;
  }

  async getProfilesWithPagination(
    freelancerId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    profiles: IFreelancerProfile[];
    total: number;
    totalPages: number;
  }> {
    // Validate freelancer exists
    const freelancer =
      await this.freelancerDAO.findFreelancerById(freelancerId);
    if (!freelancer) {
      throw new NotFoundError(
        RESPONSE_MESSAGE.FREELANCER_NOT_FOUND,
        ERROR_CODES.FREELANCER_NOT_FOUND
      );
    }

    return await this.freelancerProfileDAO.findProfilesWithPagination(
      freelancerId,
      page,
      limit
    );
  }

  private validateProfileData(
    profileData: Partial<CreateProfileRequest>
  ): void {
    console.log("Validating profile data:", profileData);

    // Profile name validation - only validate if provided and not empty
    if (
      profileData.profileName &&
      profileData.profileName.trim() &&
      (profileData.profileName.trim().length < 1 ||
        profileData.profileName.trim().length > 100)
    ) {
      throw new BadRequestError(
        "Profile name must be between 1 and 100 characters",
        "INVALID_PROFILE_NAME"
      );
    }

    // Description validation - more lenient, allow shorter descriptions
    if (
      profileData.description &&
      profileData.description.trim() &&
      (profileData.description.trim().length < 1 ||
        profileData.description.trim().length > 1000)
    ) {
      throw new BadRequestError(
        "Description must be between 1 and 1000 characters",
        "INVALID_DESCRIPTION"
      );
    }

    // Hourly rate validation
    if (profileData.hourlyRate !== undefined && profileData.hourlyRate < 0) {
      throw new BadRequestError(
        "Hourly rate must be a positive number",
        "INVALID_HOURLY_RATE"
      );
    }

    // Validate URLs if provided and not empty
    const urlFields = ["githubLink", "linkedinLink", "personalWebsite"];
    urlFields.forEach((field) => {
      const url = profileData[field as keyof CreateProfileRequest] as string;
      if (url && url.trim() && !this.isValidUrl(url.trim())) {
        throw new BadRequestError(
          `Invalid ${field}`,
          `INVALID_${field.toUpperCase()}`
        );
      }
    });

    // Validate portfolio links - only validate non-empty links
    if (
      profileData.portfolioLinks &&
      Array.isArray(profileData.portfolioLinks)
    ) {
      profileData.portfolioLinks.forEach((link, index) => {
        if (link && link.trim() && !this.isValidUrl(link.trim())) {
          throw new BadRequestError(
            `Invalid portfolio link at position ${index + 1}`,
            "INVALID_PORTFOLIO_LINK"
          );
        }
      });
    }

    console.log("Profile data validation passed");
  }

  private isValidUrl(url: string): boolean {
    try {
      // Allow URLs without protocol by adding https:// if missing
      const urlToTest =
        url.startsWith("http://") || url.startsWith("https://")
          ? url
          : `https://${url}`;

      new URL(urlToTest);
      return true;
    } catch {
      // If URL validation fails, check if it's at least a reasonable format
      const urlPattern =
        /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
      return urlPattern.test(url) || url.includes(".");
    }
  }
}
