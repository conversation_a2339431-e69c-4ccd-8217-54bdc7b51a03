import { Model } from "mongoose";
import { Service } from "fastify-decorators";

import { IFreelancerProfile } from "../models/freelancerProfile.entity";
import FreelancerProfile from "../models/freelancerProfile.entity";
import { BaseDAO } from "../common/base.dao";

@Service()
export class FreelancerProfileDAO extends BaseDAO {
  model: Model<IFreelancerProfile>;

  constructor() {
    super();
    this.model = FreelancerProfile;
  }

  async createProfile(
    profileData: Partial<IFreelancerProfile>
  ): Promise<IFreelancerProfile> {
    const profile = new this.model(profileData);
    return await profile.save();
  }

  async findProfileById(profileId: string): Promise<IFreelancerProfile | null> {
    return await this.model
      .findById(profileId)
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .exec();
  }

  async findProfilesByFreelancerId(
    freelancerId: string
  ): Promise<IFreelancerProfile[]> {
    return await this.model
      .find({ freelancerId })
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  async findActiveProfilesByFreelancerId(
    freelancerId: string
  ): Promise<IFreelancerProfile[]> {
    return await this.model
      .find({
        freelancerId,
        isActive: true,
      })
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  async updateProfile(
    profileId: string,
    updateData: Partial<IFreelancerProfile>
  ): Promise<IFreelancerProfile | null> {
    return await this.model
      .findByIdAndUpdate(
        profileId,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      )
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .exec();
  }

  async deleteProfile(profileId: string): Promise<IFreelancerProfile | null> {
    return await this.model.findByIdAndDelete(profileId).exec();
  }

  async findProfileByFreelancerIdAndName(
    freelancerId: string,
    profileName: string
  ): Promise<IFreelancerProfile | null> {
    return await this.model
      .findOne({
        freelancerId,
        profileName,
      })
      .exec();
  }

  async toggleProfileStatus(
    profileId: string,
    isActive: boolean
  ): Promise<IFreelancerProfile | null> {
    return await this.model
      .findByIdAndUpdate(
        profileId,
        { isActive, updatedAt: new Date() },
        { new: true, runValidators: true }
      )
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .exec();
  }

  async countProfilesByFreelancerId(freelancerId: string): Promise<number> {
    return await this.model
      .countDocuments({
        freelancerId,
      })
      .exec();
  }

  async findProfilesWithPagination(
    freelancerId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    profiles: IFreelancerProfile[];
    total: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;
    const total = await this.model.countDocuments({
      freelancerId,
    }).exec();
    const totalPages = Math.ceil(total / limit);

    const profiles = await this.model.find({ freelancerId })
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();

    return { profiles, total, totalPages };
  }
}
