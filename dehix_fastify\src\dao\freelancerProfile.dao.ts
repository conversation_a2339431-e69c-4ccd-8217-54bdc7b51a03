import { Model } from "mongoose";
import { Inject, Service } from "fastify-decorators";

import { IFreelancerProfile } from "../models/freelancerProfile.entity";
import { DBModels } from "../models";
import { Logger } from "../common/services/logger.service";

@Service()
export class FreelancerProfileDAO {
  @Inject("models")
  models!: DBModels;

  @Inject("logger")
  logger!: Logger;

  get FreelancerProfileModel(): Model<IFreelancerProfile> {
    return this.models.FreelancerProfileModel!;
  }

  async createProfile(
    profileData: Partial<IFreelancerProfile>
  ): Promise<IFreelancerProfile> {
    this.logger.info(
      "FreelancerProfileDAO: createProfile: Creating new profile",
      profileData
    );
    const profile = new this.FreelancerProfileModel(profileData);
    return await profile.save();
  }

  async findProfileById(profileId: string): Promise<IFreelancerProfile | null> {
    this.logger.info(
      "FreelancerProfileDAO: findProfileById: Finding profile by ID",
      profileId
    );
    return await this.FreelancerProfileModel.findById(profileId)
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .exec();
  }

  async findProfilesByFreelancerId(
    freelancerId: string
  ): Promise<IFreelancerProfile[]> {
    this.logger.info(
      "FreelancerProfileDAO: findProfilesByFreelancerId: Finding profiles for freelancer",
      freelancerId
    );
    return await this.FreelancerProfileModel.find({ freelancerId })
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  async findActiveProfilesByFreelancerId(
    freelancerId: string
  ): Promise<IFreelancerProfile[]> {
    this.logger.info(
      "FreelancerProfileDAO: findActiveProfilesByFreelancerId: Finding active profiles for freelancer",
      freelancerId
    );
    return await this.FreelancerProfileModel.find({
      freelancerId,
      isActive: true,
    })
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  async updateProfile(
    profileId: string,
    updateData: Partial<IFreelancerProfile>
  ): Promise<IFreelancerProfile | null> {
    this.logger.info("FreelancerProfileDAO: updateProfile: Updating profile", {
      profileId,
      updateData,
    });
    return await this.FreelancerProfileModel.findByIdAndUpdate(
      profileId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    )
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .exec();
  }

  async deleteProfile(profileId: string): Promise<IFreelancerProfile | null> {
    this.logger.info(
      "FreelancerProfileDAO: deleteProfile: Deleting profile",
      profileId
    );
    return await this.FreelancerProfileModel.findByIdAndDelete(
      profileId
    ).exec();
  }

  async findProfileByFreelancerIdAndName(
    freelancerId: string,
    profileName: string
  ): Promise<IFreelancerProfile | null> {
    this.logger.info(
      "FreelancerProfileDAO: findProfileByFreelancerIdAndName: Finding profile by freelancer ID and name",
      { freelancerId, profileName }
    );
    return await this.FreelancerProfileModel.findOne({
      freelancerId,
      profileName,
    }).exec();
  }

  async toggleProfileStatus(
    profileId: string,
    isActive: boolean
  ): Promise<IFreelancerProfile | null> {
    this.logger.info(
      "FreelancerProfileDAO: toggleProfileStatus: Toggling profile status",
      { profileId, isActive }
    );
    return await this.FreelancerProfileModel.findByIdAndUpdate(
      profileId,
      { isActive, updatedAt: new Date() },
      { new: true, runValidators: true }
    )
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .exec();
  }

  async countProfilesByFreelancerId(freelancerId: string): Promise<number> {
    this.logger.info(
      "FreelancerProfileDAO: countProfilesByFreelancerId: Counting profiles for freelancer",
      freelancerId
    );
    return await this.FreelancerProfileModel.countDocuments({
      freelancerId,
    }).exec();
  }

  async findProfilesWithPagination(
    freelancerId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    profiles: IFreelancerProfile[];
    total: number;
    totalPages: number;
  }> {
    this.logger.info(
      "FreelancerProfileDAO: findProfilesWithPagination: Finding profiles with pagination",
      { freelancerId, page, limit }
    );

    const skip = (page - 1) * limit;
    const total = await this.FreelancerProfileModel.countDocuments({
      freelancerId,
    }).exec();
    const totalPages = Math.ceil(total / limit);

    const profiles = await this.FreelancerProfileModel.find({ freelancerId })
      .populate("skills", "name")
      .populate("domains", "name")
      .populate({
        path: "projects",
        select:
          "projectName description role start end techUsed githubLink projectType verified",
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();

    return { profiles, total, totalPages };
  }
}
