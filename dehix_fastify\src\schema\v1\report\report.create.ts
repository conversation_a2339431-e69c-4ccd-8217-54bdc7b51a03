export const createReportSchema = {
  body: {
    type: 'object',
    required: ['subject', 'description', 'report_type', 'reportedById', 'status'],
    properties: {
      subject: { type: 'string' },
      description: { type: 'string' },
      report_type: { type: 'string', enum: ['Business', 'Freelancer'] }, // adjust enum as needed
      reportedById: { type: 'string' },
      status: { type: 'string', enum: ['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'] },
    },
  },
};
