"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_cards_freelancerProfileCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/cards/freelancerProfileCard */ \"(app-pages-browser)/./src/components/cards/freelancerProfileCard.tsx\");\n/* harmony import */ var _components_cards_addProfileCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/cards/addProfileCard */ \"(app-pages-browser)/./src/components/cards/addProfileCard.tsx\");\n/* harmony import */ var _components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dialogs/addEditProfileDialog */ \"(app-pages-browser)/./src/components/dialogs/addEditProfileDialog.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfile, setEditingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            setProfiles(response.data.data || []);\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddProfile = ()=>{\n        setEditingProfile(null);\n        setIsDialogOpen(true);\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsDialogOpen(true);\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleViewProfile = (profile)=>{\n        // TODO: Implement profile view modal or navigate to profile view page\n        console.log(\"View profile:\", profile);\n    };\n    const handleToggleStatus = async (profileId, isActive)=>{\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.patch(\"/freelancer/profile/\".concat(profileId, \"/toggle-status\"), {\n                isActive\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: isActive ? \"Profile Activated\" : \"Profile Deactivated\",\n                description: \"Profile has been \".concat(isActive ? \"activated\" : \"deactivated\", \".\")\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error updating profile status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Professional Profiles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        ...Array(3)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-[400px] w-full\"\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cards_addProfileCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            onClick: handleAddProfile\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cards_freelancerProfileCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                profile: profile,\n                                                onEdit: handleEditProfile,\n                                                onDelete: handleDeleteProfile,\n                                                onView: handleViewProfile,\n                                                onToggleStatus: handleToggleStatus\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                !isLoading && profiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles yet\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                open: isDialogOpen,\n                onOpenChange: setIsDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"CkLx0GFLTcqAKXa30ooXVDm4ThI=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});