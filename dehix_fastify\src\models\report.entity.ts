import mongoose, { Schema, Document, Model } from "mongoose";
import { v4 as uuidv4 } from "uuid";

// Main Report interface
export interface IReport extends Document {
  _id: string;
  report_type: string;
  description: string;
  subject: string;        // keep this only if you want to save it in DB
  reportedById: string;   // add this (was missing)
  status: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Add `subject` to schema if you want to keep it
const ReportSchema: Schema<IReport> = new Schema(
  {
    _id: {
      type: String,
      default: uuidv4,
    },
    report_type: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    subject: {
      type: String,    // add this
      required: true,
    },
    reportedById: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);


// Create and export the Report model
export const ReportModel: Model<IReport> = mongoose.model<IReport>(
  "Report",
  ReportSchema,
);

export default {
  ReportModel,
};
