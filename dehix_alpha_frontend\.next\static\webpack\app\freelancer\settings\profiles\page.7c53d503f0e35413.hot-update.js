"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx":
/*!*******************************************************!*\
  !*** ./src/app/freelancer/settings/profiles/page.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/menu/sidebarMenu */ \"(app-pages-browser)/./src/components/menu/sidebarMenu.tsx\");\n/* harmony import */ var _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/menuItems/freelancer/settingsMenuItems */ \"(app-pages-browser)/./src/config/menuItems/freelancer/settingsMenuItems.tsx\");\n/* harmony import */ var _components_header_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header/header */ \"(app-pages-browser)/./src/components/header/header.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Save,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dialogs/addEditProfileDialog */ \"(app-pages-browser)/./src/components/dialogs/addEditProfileDialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProfilesPage() {\n    _s();\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)((state)=>state.user);\n    const [profiles, setProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProfileName, setNewProfileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newProfileDescription, setNewProfileDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileToDelete, setProfileToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProfile, setEditingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingProfileData, setEditingProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProfiles();\n    }, [\n        user.uid\n    ]);\n    const fetchProfiles = async ()=>{\n        if (!user.uid) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.get(\"/freelancer/profiles\");\n            const profilesData = response.data.data || [];\n            setProfiles(profilesData);\n            // Set the first profile as active tab, or empty string if no profiles\n            if (profilesData.length > 0 && !activeTab && profilesData[0]._id) {\n                setActiveTab(profilesData[0]._id);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profiles:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to load profiles\",\n                variant: \"destructive\"\n            });\n            setProfiles([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCreateProfile = async ()=>{\n        if (!newProfileName.trim()) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Profile name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const description = newProfileDescription.trim() || \"Professional profile for \".concat(newProfileName.trim(), \". This profile showcases my skills and experience in this domain.\");\n        if (description.length < 10) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Description must be at least 10 characters long\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.post(\"/freelancer/profile\", {\n                profileName: newProfileName.trim(),\n                description: description,\n                skills: [],\n                domains: [],\n                projects: [],\n                experiences: [],\n                education: [],\n                portfolioLinks: []\n            });\n            const newProfile = response.data.data;\n            setProfiles([\n                ...profiles,\n                newProfile\n            ]);\n            if (newProfile._id) {\n                setActiveTab(newProfile._id);\n            }\n            setNewProfileName(\"\");\n            setNewProfileDescription(\"\");\n            setIsCreateDialogOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Success\",\n                description: \"Profile created successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error creating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to create profile\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteProfile = (profileId)=>{\n        setProfileToDelete(profileId);\n        setDeleteDialogOpen(true);\n    };\n    const confirmDeleteProfile = async ()=>{\n        if (!profileToDelete) return;\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.delete(\"/freelancer/profile/\".concat(profileToDelete));\n            // If deleting the active tab, switch to another tab\n            if (activeTab === profileToDelete) {\n                const remainingProfiles = profiles.filter((p)=>p._id !== profileToDelete);\n                setActiveTab(remainingProfiles.length > 0 && remainingProfiles[0]._id ? remainingProfiles[0]._id : \"\");\n            }\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Profile Deleted\",\n                description: \"Profile has been successfully deleted.\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error deleting profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to delete profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setDeleteDialogOpen(false);\n            setProfileToDelete(null);\n        }\n    };\n    const handleEditProfile = (profile)=>{\n        setEditingProfile(profile);\n        setIsEditDialogOpen(true);\n    };\n    const handleProfileSaved = ()=>{\n        fetchProfiles();\n        setIsEditDialogOpen(false);\n        setEditingProfile(null);\n    };\n    const handleUpdateProfile = async (profileId, updatedData)=>{\n        setIsUpdating(true);\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_5__.axiosInstance.put(\"/freelancer/profile/\".concat(profileId), updatedData);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Success\",\n                description: \"Profile updated successfully\"\n            });\n            fetchProfiles();\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: \"Error\",\n                description: \"Failed to update profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const handleInputChange = (profileId, field, value)=>{\n        setEditingProfileData((prev)=>({\n                ...prev,\n                [profileId]: {\n                    ...prev[profileId],\n                    [field]: value\n                }\n            }));\n    };\n    const getProfileData = (profile)=>{\n        return editingProfileData[profile._id] || profile;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen w-full flex-col bg-muted/40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_menu_sidebarMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                active: \"Profiles\",\n                isKycCheck: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        menuItemsTop: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsTop,\n                        menuItemsBottom: _config_menuItems_freelancer_settingsMenuItems__WEBPACK_IMPORTED_MODULE_3__.menuItemsBottom,\n                        activeMenu: \"Profiles\",\n                        breadcrumbItems: [\n                            {\n                                label: \"Freelancer\",\n                                link: \"/dashboard/freelancer\"\n                            },\n                            {\n                                label: \"Settings\",\n                                link: \"#\"\n                            },\n                            {\n                                label: \"Profiles\",\n                                link: \"#\"\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"grid flex-1 items-start sm:px-6 sm:py-0 md:gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"Professional Profiles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Create and manage multiple professional profiles to showcase different aspects of your expertise.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>setIsCreateDialogOpen(true),\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Add Profile\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Loading profiles...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this) : profiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"No profiles added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-4\",\n                                            children: \"Create your first professional profile to get started.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTab(profile._id),\n                                                    className: \"px-6 py-3 text-sm font-medium border-b-2 transition-colors duration-200 \".concat(activeTab === profile._id ? \"text-blue-600 border-blue-600 bg-blue-50\" : \"text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: profile.profileName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    handleDeleteProfile(profile._id);\n                                                                },\n                                                                className: \"ml-1 text-red-500 hover:text-red-700 opacity-70 hover:opacity-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, profile._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        profiles.filter((profile)=>profile._id).map((profile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(activeTab === profile._id ? \"block\" : \"hidden\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-200 p-6 bg-black text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center pb-4 border-b border-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-semibold text-white\",\n                                                                        children: getProfileData(profile).profileName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleUpdateProfile(profile._id, getProfileData(profile)),\n                                                                                disabled: isUpdating,\n                                                                                className: \"flex items-center gap-2 bg-white text-black hover:bg-gray-200\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 344,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    isUpdating ? \"Updating...\" : \"Update\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"destructive\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleDeleteProfile(profile._id),\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Delete\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"profileName-\".concat(profile._id),\n                                                                                        children: \"Profile Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 365,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"profileName-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).profileName || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"profileName\", e.target.value),\n                                                                                        placeholder: \"e.g., Frontend Developer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"hourlyRate-\".concat(profile._id),\n                                                                                        children: \"Hourly Rate ($)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 384,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"hourlyRate-\".concat(profile._id),\n                                                                                        type: \"number\",\n                                                                                        value: getProfileData(profile).hourlyRate || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"hourlyRate\", parseFloat(e.target.value) || 0),\n                                                                                        placeholder: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 387,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 383,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                htmlFor: \"description-\".concat(profile._id),\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 407,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                                                id: \"description-\".concat(profile._id),\n                                                                                value: getProfileData(profile).description || \"\",\n                                                                                onChange: (e)=>handleInputChange(profile._id, \"description\", e.target.value),\n                                                                                placeholder: \"Describe your expertise and experience...\",\n                                                                                rows: 4\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        children: \"Skills\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 430,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                                    placeholder: \"Select skills\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                    lineNumber: 433,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 432,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"javascript\",\n                                                                                                        children: \"JavaScript\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 436,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"react\",\n                                                                                                        children: \"React\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 439,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"nodejs\",\n                                                                                                        children: \"Node.js\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 440,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"python\",\n                                                                                                        children: \"Python\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 443,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 435,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 431,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                                                        children: profile.skills && profile.skills.length > 0 ? profile.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                children: [\n                                                                                                    typeof skill === \"string\" ? skill : skill.label || skill.name,\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                        className: \"h-3 w-3 ml-1 cursor-pointer\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 457,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, index, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 453,\n                                                                                                columnNumber: 41\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-muted-foreground text-sm\",\n                                                                                            children: \"No skills selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                            lineNumber: 462,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 448,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        children: \"Domains\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 469,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                                    placeholder: \"Select domains\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                    lineNumber: 472,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 471,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"frontend\",\n                                                                                                        children: \"Frontend\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 475,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"backend\",\n                                                                                                        children: \"Backend\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 478,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"fullstack\",\n                                                                                                        children: \"Full Stack\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 481,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"mobile\",\n                                                                                                        children: \"Mobile\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 484,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 474,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 470,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                                                        children: profile.domains && profile.domains.length > 0 ? profile.domains.map((domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                children: [\n                                                                                                    typeof domain === \"string\" ? domain : domain.label || domain.name,\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                        className: \"h-3 w-3 ml-1 cursor-pointer\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 498,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, index, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 494,\n                                                                                                columnNumber: 41\n                                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-muted-foreground text-sm\",\n                                                                                            children: \"No domains selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                            lineNumber: 503,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 489,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"githubLink-\".concat(profile._id),\n                                                                                        children: \"GitHub Link\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 514,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"githubLink-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).githubLink || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"githubLink\", e.target.value),\n                                                                                        placeholder: \"https://github.com/username\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 517,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"linkedinLink-\".concat(profile._id),\n                                                                                        children: \"LinkedIn Link\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 533,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"linkedinLink-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).linkedinLink || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"linkedinLink\", e.target.value),\n                                                                                        placeholder: \"https://linkedin.com/in/username\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 536,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"personalWebsite-\".concat(profile._id),\n                                                                                        children: \"Personal Website\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 555,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                                        id: \"personalWebsite-\".concat(profile._id),\n                                                                                        value: getProfileData(profile).personalWebsite || \"\",\n                                                                                        onChange: (e)=>handleInputChange(profile._id, \"personalWebsite\", e.target.value),\n                                                                                        placeholder: \"https://yourwebsite.com\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 560,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 554,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                                        htmlFor: \"availability-\".concat(profile._id),\n                                                                                        children: \"Availability\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 577,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                                                        value: getProfileData(profile).availability || \"FREELANCE\",\n                                                                                        onValueChange: (value)=>handleInputChange(profile._id, \"availability\", value),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectTrigger, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectValue, {\n                                                                                                    placeholder: \"Select availability\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                    lineNumber: 594,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 593,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectContent, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"FULL_TIME\",\n                                                                                                        children: \"Full Time\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 597,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"PART_TIME\",\n                                                                                                        children: \"Part Time\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 600,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"CONTRACT\",\n                                                                                                        children: \"Contract\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 603,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_14__.SelectItem, {\n                                                                                                        value: \"FREELANCE\",\n                                                                                                        children: \"Freelance\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                        lineNumber: 606,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                                lineNumber: 596,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 580,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>handleEditProfile(profile),\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 621,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Add Projects\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 616,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>handleEditProfile(profile),\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 629,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Add Experience\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>handleEditProfile(profile),\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Save_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                        lineNumber: 637,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Add Education\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, profile._id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: isCreateDialogOpen,\n                onOpenChange: setIsCreateDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Create New Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Enter a name and description for your new professional profile.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profile Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            placeholder: \"e.g., Frontend Developer, Backend Engineer\",\n                                            value: newProfileName,\n                                            onChange: (e)=>setNewProfileName(e.target.value),\n                                            className: \"mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Description (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                            placeholder: \"Describe your expertise and experience in this area... (minimum 10 characters if provided)\",\n                                            value: newProfileDescription,\n                                            onChange: (e)=>setNewProfileDescription(e.target.value),\n                                            className: \"mt-1\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-1\",\n                                            children: \"If left empty, a default description will be generated.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setIsCreateDialogOpen(false);\n                                        setNewProfileName(\"\");\n                                        setNewProfileDescription(\"\");\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleCreateProfile,\n                                    children: \"Create Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 653,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dialogs_addEditProfileDialog__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                profile: editingProfile,\n                onProfileSaved: handleProfileSaved,\n                freelancerId: user.uid\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 704,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Delete Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this profile? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDeleteProfile,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\app\\\\freelancer\\\\settings\\\\profiles\\\\page.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilesPage, \"U+O9vP484Bv2luCW8SslB9VfNro=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector\n    ];\n});\n_c = ProfilesPage;\nvar _c;\n$RefreshReg$(_c, \"ProfilesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/freelancer/settings/profiles/page.tsx\n"));

/***/ })

});