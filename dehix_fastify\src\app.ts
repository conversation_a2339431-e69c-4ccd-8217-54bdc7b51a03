import fastify from "fastify";
import fastifyEnv from "@fastify/env";
import { bootstrap } from "fastify-decorators";
import cors from "@fastify/cors";
import { initializeClients } from "./clients";
import swagger from "@fastify/swagger";
import swagger_ui from "@fastify/swagger-ui";
import { logger } from "./common/services/logger.service";
import fs from "fs";
import fastifyMultipart from "@fastify/multipart";

// Import models to ensure they are registered with Mongoose
import "./models";

// Env schema
const schema = {
  type: "object",
  required: [],
  patternProperties: {
    "SERVER_(.*)": { type: "string" },
  },
  // add key properties for specific property validation
};

const app = fastify({
  logger: logger,
  pluginTimeout: 30000, // Increase plugin timeout to 30 seconds
});

// Env path for stages
const envPath = process.env.NODE_ENV
  ? `./.env.${process.env.NODE_ENV}`
  : "./.env";

const packageJSON = JSON.parse(fs.readFileSync("./package.json", "utf8"));

export const configure = async () => {
  try {
    // Register environment variables first
    await app.register(fastifyEnv, {
      schema: schema,
      dotenv: { path: envPath },
      data: process.env,
    });

    // Initialize clients (database connections, etc.) early
    await app.register(initializeClients);

    // Register CORS
    await app.register(cors, {
      origin: ["*"],
      methods: ["OPTIONS", "GET", "PUT", "PATCH", "POST", "DELETE"],
      allowedHeaders: [
        "Content-Type",
        "Authorization",
        "Accept",
        "Origin",
        "X-Requested-With",
        "x-api-key",
      ],
    });

    // Register multipart support
    await app.register(fastifyMultipart);

    // Register swagger documentation
    await app.register(swagger, {
      mode: "dynamic",
      swagger: {
        info: {
          title: packageJSON.title,
          description: packageJSON.description,
          version: packageJSON.version,
          contact: {
            name: packageJSON.author,
            url: packageJSON.website,
            email: packageJSON.email,
          },
        },
        schemes: ["http", "https"],
        consumes: ["application/json"],
        produces: ["application/json"],
      },
      openapi: {
        info: {
          title: packageJSON.title,
          description: packageJSON.description,
          version: packageJSON.version,
          contact: {
            name: packageJSON.author,
            url: packageJSON.website,
            email: packageJSON.email,
          },
        },
        components: {
          securitySchemes: {
            bearerAuth: {
              type: "apiKey",
              name: "Authorization",
              in: "header",
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
    });

    await app.register(swagger_ui, {
      routePrefix: "/documentation",
      uiConfig: {
        docExpansion: "none",
        deepLinking: true,
      },
      staticCSP: false,
      transformStaticCSP: (header) => header,
      transformSpecification: (swaggerObject) => {
        return swaggerObject;
      },
      transformSpecificationClone: true,
    });

    // Register fastify-decorators bootstrap LAST
    await app.register(bootstrap, {
      directory: new URL(`controllers`, import.meta.url),
      mask: /\.controller\./,
    });

    await app.ready();
    console.log("All plugins loaded successfully");
  } catch (error) {
    console.error("An error occurred during initialization:", error);
    process.exit(1);
  }

  if (!global.LAMBDA_ENV) {
    console.log("Running App env");
    app.listen(
      {
        port: Number(process.env.SERVER_PORT),
      },
      (err: any) => {
        if (err) console.error(err);
        console.log(`server listening on ${process.env.SERVER_PORT}`);
      }
    );
  }
};

if (!global.LAMBDA_ENV) {
  configure();
}

export default app;
