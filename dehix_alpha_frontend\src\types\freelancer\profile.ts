export interface Skill {
  _id?: string;
  name: string;
  level?: string;
  experience?: string;
}

export interface Domain {
  _id?: string;
  name: string;
  level?: string;
}

export interface Project {
  _id?: string;
  projectName: string;
  description: string;
  role: string;
  start: string;
  end: string;
  techUsed: string[];
  githubLink?: string;
  projectType?: string;
  verified?: boolean;
}

export interface ProfessionalExperience {
  _id?: string;
  company: string;
  jobTitle: string;
  workDescription: string;
  workFrom: string;
  workTo: string;
  referencePersonName?: string;
  referencePersonContact?: string;
  githubRepoLink?: string;
}

export interface Education {
  _id?: string;
  degree: string;
  universityName: string;
  fieldOfStudy: string;
  startDate: string;
  endDate: string;
  grade: string;
}

export interface FreelancerProfile {
  _id?: string;
  profileName: string;
  description: string;
  skills: Skill[];
  domains: Domain[];
  projects: Project[];
  experiences: ProfessionalExperience[];
  education: Education[];
  portfolioLinks?: string[];
  githubLink?: string;
  linkedinLink?: string;
  personalWebsite?: string;
  hourlyRate?: number;
  availability?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'FREELANCE';
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateProfileRequest {
  profileName: string;
  description: string;
  skills: string[];
  domains: string[];
  projects: string[];
  experiences: string[];
  education: string[];
  portfolioLinks?: string[];
  githubLink?: string;
  linkedinLink?: string;
  personalWebsite?: string;
  hourlyRate?: number;
  availability?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'FREELANCE';
}

export interface UpdateProfileRequest extends Partial<CreateProfileRequest> {
  _id: string;
  isActive?: boolean;
}

export interface ProfileFormData {
  profileName: string;
  description: string;
  selectedSkills: string[];
  selectedDomains: string[];
  selectedProjects: string[];
  selectedExperiences: string[];
  selectedEducation: string[];
  portfolioLinks: string[];
  githubLink: string;
  linkedinLink: string;
  personalWebsite: string;
  hourlyRate: string;
  availability: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'FREELANCE';
}
